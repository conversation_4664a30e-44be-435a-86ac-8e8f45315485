import mqtt, { MqttClient } from "mqtt";
import { storage } from "./storage";
import type { Devi<PERSON>, InsertSensorReading } from "@shared/schema";

interface MqttConfig {
  host: string;
  port: number;
  clientId: string;
  username?: string;
  password?: string;
}

interface MqttMessage {
  // Legacy format (for backward compatibility)
  leftBankPressure?: string;
  rightBankPressure?: string;
  leftBankStatus?: string;
  rightBankStatus?: string;
  serialNumber?: string;
  timestamp?: string;

  // Actual format from cylinder_manifold topics
  SERNO?: string;
  DEVID?: string;
  "Left Bank Pressure"?: string;
  "Right Bank Pressure"?: string;
  "Left Bank Status"?: string;
  "Right Bank Status"?: string;
  "Hospital Name"?: string;
  Location?: string;
}

export class MqttService {
  private client: MqttClient | null = null;
  private config: MqttConfig;
  private subscribedTopics: Set<string> = new Set();
  private deviceTopicMap: Map<string, number> = new Map(); // topic -> deviceId mapping
  private broadcastCallback?: (data: any) => void;

  constructor(config: MqttConfig) {
    this.config = config;
  }

  /**
   * Initialize MQTT connection
   */
  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const connectUrl = `mqtt://${this.config.host}:${this.config.port}`;

        const options: mqtt.IClientOptions = {
          clientId: this.config.clientId,
          clean: true,
          connectTimeout: 4000,
          reconnectPeriod: 1000,
        };

        // Add authentication if provided
        if (this.config.username) {
          options.username = this.config.username;
        }
        if (this.config.password) {
          options.password = this.config.password;
        }

        console.log(`Connecting to MQTT broker at ${connectUrl}...`);
        this.client = mqtt.connect(connectUrl, options);

        this.client.on("connect", () => {
          console.log("MQTT client connected successfully");
          resolve();
        });

        this.client.on("error", (error) => {
          console.error("MQTT connection error:", error);
          reject(error);
        });

        this.client.on("message", this.handleMessage.bind(this));

        this.client.on("close", () => {
          console.log("MQTT connection closed");
        });

        this.client.on("reconnect", () => {
          console.log("MQTT client reconnecting...");
        });
      } catch (error) {
        console.error("Failed to create MQTT client:", error);
        reject(error);
      }
    });
  }

  /**
   * Set the broadcast callback for real-time updates
   */
  setBroadcastCallback(callback: (data: any) => void): void {
    this.broadcastCallback = callback;
  }

  /**
   * Subscribe to MQTT topics for all active devices
   */
  async subscribeToDeviceTopics(): Promise<void> {
    try {
      const devices = await storage.getDevices();
      const activeDevices = devices.filter(
        (device) => device.isActive && device.mqttTopic
      );

      for (const device of activeDevices) {
        await this.subscribeToTopic(device.mqttTopic, device.id);
      }

      console.log(`Subscribed to ${activeDevices.length} device topics`);
    } catch (error) {
      console.error("Error subscribing to device topics:", error);
    }
  }

  /**
   * Subscribe to a specific MQTT topic
   */
  async subscribeToTopic(topic: string, deviceId: number): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        reject(new Error("MQTT client not connected"));
        return;
      }

      if (this.subscribedTopics.has(topic)) {
        console.log(`Already subscribed to topic: ${topic}`);
        resolve();
        return;
      }

      this.client.subscribe(topic, (error) => {
        if (error) {
          console.error(`Failed to subscribe to topic ${topic}:`, error);
          reject(error);
        } else {
          console.log(`Successfully subscribed to topic: ${topic}`);
          this.subscribedTopics.add(topic);
          this.deviceTopicMap.set(topic, deviceId);
          resolve();
        }
      });
    });
  }

  /**
   * Unsubscribe from a specific MQTT topic
   */
  async unsubscribeFromTopic(topic: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.client) {
        reject(new Error("MQTT client not connected"));
        return;
      }

      if (!this.subscribedTopics.has(topic)) {
        console.log(`Not subscribed to topic: ${topic}`);
        resolve();
        return;
      }

      this.client.unsubscribe(topic, (error) => {
        if (error) {
          console.error(`Failed to unsubscribe from topic ${topic}:`, error);
          reject(error);
        } else {
          console.log(`Successfully unsubscribed from topic: ${topic}`);
          this.subscribedTopics.delete(topic);
          this.deviceTopicMap.delete(topic);
          resolve();
        }
      });
    });
  }

  /**
   * Handle incoming MQTT messages
   */
  private async handleMessage(topic: string, message: Buffer): Promise<void> {
    try {
      const deviceId = this.deviceTopicMap.get(topic);
      if (!deviceId) {
        console.warn(`Received message for unknown topic: ${topic}`);
        return;
      }

      // Parse the MQTT message
      const messageStr = message.toString();
      console.log(`Received MQTT message on topic ${topic}:`, messageStr);

      let mqttData: MqttMessage;
      try {
        mqttData = JSON.parse(messageStr);
      } catch (parseError) {
        console.error(`Failed to parse MQTT message as JSON:`, parseError);
        return;
      }

      // Convert MQTT message to sensor reading format
      // Handle both legacy format and actual cylinder_manifold format
      const leftBankPressure =
        mqttData.leftBankPressure ||
        (mqttData["Left Bank Pressure"]
          ? this.extractNumericValue(mqttData["Left Bank Pressure"])
          : "0");
      const rightBankPressure =
        mqttData.rightBankPressure ||
        (mqttData["Right Bank Pressure"]
          ? this.extractNumericValue(mqttData["Right Bank Pressure"])
          : "0");
      const leftBankStatus =
        mqttData.leftBankStatus || mqttData["Left Bank Status"] || "Normal";
      const rightBankStatus =
        mqttData.rightBankStatus || mqttData["Right Bank Status"] || "Normal";
      const serialNumber =
        mqttData.serialNumber ||
        mqttData.SERNO ||
        `MQTT-${deviceId}-${Date.now()}`;

      const sensorReading: InsertSensorReading = {
        deviceId: deviceId,
        leftBankPressure: leftBankPressure,
        rightBankPressure: rightBankPressure,
        leftBankStatus: leftBankStatus,
        rightBankStatus: rightBankStatus,
        serialNumber: serialNumber,
      };

      // Store the sensor reading in the database
      const savedReading = await storage.createSensorReading(sensorReading);
      console.log(`Saved sensor reading from MQTT:`, savedReading);

      // Update device status based on sensor readings
      await this.updateDeviceStatus(deviceId, sensorReading);

      // Broadcast real-time update via WebSocket
      if (this.broadcastCallback) {
        this.broadcastCallback({
          type: "sensor_update",
          deviceId: deviceId,
          reading: savedReading,
          source: "mqtt",
        });
      }
    } catch (error) {
      console.error(`Error handling MQTT message for topic ${topic}:`, error);
    }
  }

  /**
   * Extract numeric value from pressure strings like "52.77 Kgcm2"
   */
  private extractNumericValue(value: string): string {
    const match = value.match(/(\d+\.?\d*)/);
    return match ? match[1] : "0";
  }

  /**
   * Update device status based on sensor readings
   */
  private async updateDeviceStatus(
    deviceId: number,
    reading: InsertSensorReading
  ): Promise<void> {
    try {
      const leftPressure = parseFloat(reading.leftBankPressure || "0");
      const rightPressure = parseFloat(reading.rightBankPressure || "0");

      // Determine overall status based on pressure readings
      const leftStatus = reading.leftBankStatus?.toLowerCase() || "normal";
      const rightStatus = reading.rightBankStatus?.toLowerCase() || "normal";

      let overallStatus = "normal";
      if (
        leftStatus === "critical" ||
        rightStatus === "critical" ||
        leftPressure < 85 ||
        rightPressure < 85
      ) {
        overallStatus = "critical";
      } else if (
        leftStatus === "warning" ||
        rightStatus === "warning" ||
        leftPressure < 90 ||
        rightPressure < 90
      ) {
        overallStatus = "warning";
      }

      // Update device status
      await storage.updateDevice(deviceId, { status: overallStatus });

      // Create alert if needed
      if (overallStatus === "warning" || overallStatus === "critical") {
        const device = await storage.getDevice(deviceId);
        if (device) {
          await storage.createAlert({
            deviceId: deviceId,
            alertType: "pressure_threshold",
            severity: overallStatus,
            message: `${
              overallStatus === "critical" ? "Critical" : "Low"
            } pressure detected on ${device.deviceId} via MQTT`,
            isAcknowledged: false,
          });
        }
      }
    } catch (error) {
      console.error(
        `Error updating device status for device ${deviceId}:`,
        error
      );
    }
  }

  /**
   * Add a new device topic subscription
   */
  async addDeviceSubscription(device: Device): Promise<void> {
    if (device.isActive && device.mqttTopic) {
      await this.subscribeToTopic(device.mqttTopic, device.id);
    }
  }

  /**
   * Remove a device topic subscription
   */
  async removeDeviceSubscription(device: Device): Promise<void> {
    if (device.mqttTopic) {
      await this.unsubscribeFromTopic(device.mqttTopic);
    }
  }

  /**
   * Disconnect from MQTT broker
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      this.client.end();
      this.client = null;
      this.subscribedTopics.clear();
      this.deviceTopicMap.clear();
      console.log("MQTT client disconnected");
    }
  }

  /**
   * Check if MQTT client is connected
   */
  isConnected(): boolean {
    return this.client?.connected || false;
  }

  /**
   * Get list of subscribed topics
   */
  getSubscribedTopics(): string[] {
    return Array.from(this.subscribedTopics);
  }
}

// Create and export MQTT service instance
const mqttConfig: MqttConfig = {
  host: process.env.MQTT_HOST || "localhost",
  port: parseInt(process.env.MQTT_PORT || "1883"),
  clientId: process.env.MQTT_CLIENT_ID || "smart_hospital_monitor",
  username: process.env.MQTT_USERNAME || undefined,
  password: process.env.MQTT_PASSWORD || undefined,
};

export const mqttService = new MqttService(mqttConfig);
