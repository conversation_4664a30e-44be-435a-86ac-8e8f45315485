import {
  devices,
  sensorReadings,
  alerts,
  type Device,
  type InsertDevice,
  type SensorReading,
  type InsertSensorReading,
  type Alert,
  type InsertAlert,
  type User,
  type InsertUser,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, gte, lte, sql } from "drizzle-orm";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Device methods
  getDevices(): Promise<Device[]>;
  getDevice(id: number): Promise<Device | undefined>;
  getDeviceByDeviceId(deviceId: string): Promise<Device | undefined>;
  createDevice(device: InsertDevice): Promise<Device>;
  updateDevice(
    id: number,
    device: Partial<InsertDevice>
  ): Promise<Device | undefined>;
  deleteDevice(id: number): Promise<boolean>;

  // Sensor reading methods
  getSensorReadings(
    deviceId?: number,
    from?: Date,
    to?: Date
  ): Promise<SensorReading[]>;
  createSensorReading(reading: InsertSensorReading): Promise<SensorReading>;
  getLatestSensorReadings(): Promise<SensorReading[]>;

  // Alert methods
  getAlerts(deviceId?: number): Promise<Alert[]>;
  createAlert(alert: InsertAlert): Promise<Alert>;
  acknowledgeAlert(id: number): Promise<boolean>;
  getUnacknowledgedAlerts(): Promise<Alert[]>;
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  // Device methods
  async getDevices(): Promise<Device[]> {
    return await db.select().from(devices).orderBy(desc(devices.createdAt));
  }

  async getDevice(id: number): Promise<Device | undefined> {
    const [device] = await db.select().from(devices).where(eq(devices.id, id));
    return device || undefined;
  }

  async getDeviceByDeviceId(deviceId: string): Promise<Device | undefined> {
    const [device] = await db
      .select()
      .from(devices)
      .where(eq(devices.deviceId, deviceId));
    return device || undefined;
  }

  async createDevice(device: InsertDevice): Promise<Device> {
    const [newDevice] = await db.insert(devices).values(device).returning();
    return newDevice;
  }

  async updateDevice(
    id: number,
    device: Partial<InsertDevice>
  ): Promise<Device | undefined> {
    const [updatedDevice] = await db
      .update(devices)
      .set({ ...device, updatedAt: new Date() })
      .where(eq(devices.id, id))
      .returning();
    return updatedDevice || undefined;
  }

  async deleteDevice(id: number): Promise<boolean> {
    try {
      console.log(`Starting database transaction to delete device ${id}`);

      // Start a transaction to ensure all deletions succeed or fail together
      await db.transaction(async (tx) => {
        console.log(`Deleting sensor readings for device ${id}`);
        const sensorReadingsResult = await tx
          .delete(sensorReadings)
          .where(eq(sensorReadings.deviceId, id));
        console.log(
          `Deleted ${sensorReadingsResult.rowCount || 0} sensor readings`
        );

        console.log(`Deleting alerts for device ${id}`);
        const alertsResult = await tx
          .delete(alerts)
          .where(eq(alerts.deviceId, id));
        console.log(`Deleted ${alertsResult.rowCount || 0} alerts`);

        console.log(`Deleting device ${id}`);
        const deviceResult = await tx.delete(devices).where(eq(devices.id, id));
        console.log(`Deleted ${deviceResult.rowCount || 0} device records`);

        if (!deviceResult.rowCount || deviceResult.rowCount === 0) {
          throw new Error(`Device ${id} not found or already deleted`);
        }
      });

      console.log(`Successfully completed transaction for device ${id}`);
      return true;
    } catch (error) {
      console.error(`Failed to delete device ${id}:`, error);
      return false;
    }
  }

  // Sensor reading methods
  async getSensorReadings(
    deviceId?: number,
    from?: Date,
    to?: Date
  ): Promise<SensorReading[]> {
    let query = db.select().from(sensorReadings);

    const conditions = [];
    if (deviceId) {
      conditions.push(eq(sensorReadings.deviceId, deviceId));
    }
    if (from) {
      conditions.push(gte(sensorReadings.timestamp, from));
    }
    if (to) {
      conditions.push(lte(sensorReadings.timestamp, to));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    return await query.orderBy(desc(sensorReadings.timestamp));
  }

  async createSensorReading(
    reading: InsertSensorReading
  ): Promise<SensorReading> {
    const [newReading] = await db
      .insert(sensorReadings)
      .values(reading)
      .returning();
    return newReading;
  }

  async getLatestSensorReadings(): Promise<SensorReading[]> {
    return await db
      .select()
      .from(sensorReadings)
      .orderBy(desc(sensorReadings.timestamp))
      .limit(50);
  }

  // Alert methods
  async getAlerts(deviceId?: number): Promise<Alert[]> {
    let query = db.select().from(alerts);

    if (deviceId) {
      query = query.where(eq(alerts.deviceId, deviceId));
    }

    return await query.orderBy(desc(alerts.createdAt));
  }

  async createAlert(alert: InsertAlert): Promise<Alert> {
    const [newAlert] = await db.insert(alerts).values(alert).returning();
    return newAlert;
  }

  async acknowledgeAlert(id: number): Promise<boolean> {
    const result = await db
      .update(alerts)
      .set({ isAcknowledged: true, acknowledgedAt: new Date() })
      .where(eq(alerts.id, id));
    return result.rowCount ? result.rowCount > 0 : false;
  }

  async getUnacknowledgedAlerts(): Promise<Alert[]> {
    return await db
      .select()
      .from(alerts)
      .where(eq(alerts.isAcknowledged, false))
      .orderBy(desc(alerts.createdAt));
  }
}

export const storage = new DatabaseStorage();
