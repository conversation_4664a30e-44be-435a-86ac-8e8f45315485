import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import {
  insertDeviceSchema,
  insertSensorReadingSchema,
  insertAlertSchema,
} from "@shared/schema";
import { z } from "zod";
import { mqttService } from "./mqtt";

export async function registerRoutes(app: Express): Promise<Server> {
  // Device routes
  app.get("/api/devices", async (req, res) => {
    try {
      const devices = await storage.getDevices();
      res.json(devices);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch devices" });
    }
  });

  app.get("/api/devices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const device = await storage.getDevice(id);
      if (!device) {
        return res.status(404).json({ error: "Device not found" });
      }
      res.json(device);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch device" });
    }
  });

  app.post("/api/devices", async (req, res) => {
    try {
      const deviceData = insertDeviceSchema.parse(req.body);
      const device = await storage.createDevice(deviceData);

      // Subscribe to MQTT topic if device is active and has a topic
      if (device.isActive && device.mqttTopic && mqttService.isConnected()) {
        try {
          await mqttService.addDeviceSubscription(device);
          console.log(
            `Subscribed to MQTT topic for new device: ${device.deviceId}`
          );
        } catch (mqttError) {
          console.error(
            `Failed to subscribe to MQTT topic for device ${device.deviceId}:`,
            mqttError
          );
        }
      }

      res.status(201).json(device);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ error: "Invalid device data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create device" });
    }
  });

  app.put("/api/devices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deviceData = insertDeviceSchema.partial().parse(req.body);

      // Get the old device data to handle MQTT subscription changes
      const oldDevice = await storage.getDevice(id);
      if (!oldDevice) {
        return res.status(404).json({ error: "Device not found" });
      }

      const device = await storage.updateDevice(id, deviceData);
      if (!device) {
        return res.status(404).json({ error: "Device not found" });
      }

      // Handle MQTT subscription changes
      if (mqttService.isConnected()) {
        try {
          // If MQTT topic changed, unsubscribe from old topic
          if (oldDevice.mqttTopic && oldDevice.mqttTopic !== device.mqttTopic) {
            await mqttService.removeDeviceSubscription(oldDevice);
          }

          // Subscribe to new topic if device is active and has a topic
          if (device.isActive && device.mqttTopic) {
            await mqttService.addDeviceSubscription(device);
          } else if (!device.isActive && oldDevice.mqttTopic) {
            // Unsubscribe if device is deactivated
            await mqttService.removeDeviceSubscription(oldDevice);
          }
        } catch (mqttError) {
          console.error(
            `Failed to update MQTT subscription for device ${device.deviceId}:`,
            mqttError
          );
        }
      }

      res.json(device);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ error: "Invalid device data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to update device" });
    }
  });

  app.delete("/api/devices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      // Get device data before deletion to handle MQTT unsubscription
      const device = await storage.getDevice(id);
      if (!device) {
        return res.status(404).json({ error: "Device not found" });
      }

      console.log(
        `Starting deletion process for device ${device.deviceId} (ID: ${id})`
      );

      // Unsubscribe from MQTT topic before deleting
      if (device.mqttTopic && mqttService.isConnected()) {
        try {
          await mqttService.removeDeviceSubscription(device);
          console.log(
            `Unsubscribed from MQTT topic for deleted device: ${device.deviceId}`
          );
        } catch (mqttError) {
          console.error(
            `Failed to unsubscribe from MQTT topic for device ${device.deviceId}:`,
            mqttError
          );
          // Continue with deletion even if MQTT unsubscription fails
        }
      }

      // Delete the device and all related data
      const success = await storage.deleteDevice(id);
      if (!success) {
        console.error(
          `Failed to delete device ${device.deviceId} from database`
        );
        return res
          .status(500)
          .json({ error: "Failed to delete device from database" });
      }

      console.log(
        `Successfully deleted device ${device.deviceId} and all related data`
      );
      res.json({ message: "Device deleted successfully" });
    } catch (error) {
      console.error("Error in device deletion:", error);
      res.status(500).json({ error: "Failed to delete device" });
    }
  });

  // Sensor reading routes
  app.get("/api/sensor-readings", async (req, res) => {
    try {
      const deviceId = req.query.device_id
        ? parseInt(req.query.device_id as string)
        : undefined;
      const from = req.query.from
        ? new Date(req.query.from as string)
        : undefined;
      const to = req.query.to ? new Date(req.query.to as string) : undefined;

      const readings = await storage.getSensorReadings(deviceId, from, to);
      res.json(readings);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch sensor readings" });
    }
  });

  app.get("/api/sensor-readings/latest", async (req, res) => {
    try {
      const readings = await storage.getLatestSensorReadings();
      res.json(readings);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch latest readings" });
    }
  });

  app.post("/api/sensor-readings", async (req, res) => {
    try {
      const readingData = insertSensorReadingSchema.parse(req.body);
      const reading = await storage.createSensorReading(readingData);
      res.status(201).json(reading);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ error: "Invalid reading data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create sensor reading" });
    }
  });

  // Alert routes
  app.get("/api/alerts", async (req, res) => {
    try {
      const deviceId = req.query.device_id
        ? parseInt(req.query.device_id as string)
        : undefined;
      const alerts = await storage.getAlerts(deviceId);
      res.json(alerts);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch alerts" });
    }
  });

  app.get("/api/alerts/unacknowledged", async (req, res) => {
    try {
      const alerts = await storage.getUnacknowledgedAlerts();
      res.json(alerts);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch unacknowledged alerts" });
    }
  });

  app.post("/api/alerts", async (req, res) => {
    try {
      const alertData = insertAlertSchema.parse(req.body);
      const alert = await storage.createAlert(alertData);
      res.status(201).json(alert);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ error: "Invalid alert data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create alert" });
    }
  });

  app.patch("/api/alerts/:id/acknowledge", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.acknowledgeAlert(id);
      if (!success) {
        return res.status(404).json({ error: "Alert not found" });
      }
      res.json({ message: "Alert acknowledged successfully" });
    } catch (error) {
      res.status(500).json({ error: "Failed to acknowledge alert" });
    }
  });

  // MQTT status route
  app.get("/api/mqtt/status", async (req, res) => {
    try {
      const status = {
        connected: mqttService.isConnected(),
        subscribedTopics: mqttService.getSubscribedTopics(),
        config: {
          host: process.env.MQTT_HOST || "localhost",
          port: process.env.MQTT_PORT || "1883",
          clientId: process.env.MQTT_CLIENT_ID || "smart_hospital_monitor",
        },
      };
      res.json(status);
    } catch (error) {
      res.status(500).json({ error: "Failed to get MQTT status" });
    }
  });

  // Export route with enhanced functionality
  app.get("/api/export", async (req, res) => {
    try {
      const deviceId = req.query.device_id
        ? parseInt(req.query.device_id as string)
        : undefined;
      const from = req.query.from
        ? new Date(req.query.from as string)
        : undefined;
      const to = req.query.to ? new Date(req.query.to as string) : undefined;
      const format = (req.query.format as string) || "csv";
      const fields = req.query.fields
        ? (req.query.fields as string).split(",")
        : undefined;

      // Validate date range
      if (from && to && from > to) {
        return res
          .status(400)
          .json({
            error: "Invalid date range: 'from' date must be before 'to' date",
          });
      }

      const readings = await storage.getSensorReadings(deviceId, from, to);

      if (readings.length === 0) {
        return res
          .status(404)
          .json({ error: "No data found for the specified criteria" });
      }

      // Define all available fields with their display names
      const availableFields = {
        deviceId: "Device ID",
        timestamp: "Timestamp",
        leftBankPressure: "Left Bank Pressure (Kgcm2)",
        rightBankPressure: "Right Bank Pressure (Kgcm2)",
        leftBankStatus: "Left Bank Status",
        rightBankStatus: "Right Bank Status",
        leftBankCapacity: "Left Bank Capacity (Kgcm2)",
        rightBankCapacity: "Right Bank Capacity (Kgcm2)",
        leftBankPercentage: "Left Bank Percentage (%)",
        rightBankPercentage: "Right Bank Percentage (%)",
        leftBankSourceType: "Left Bank Source Type",
        rightBankSourceType: "Right Bank Source Type",
        hospitalName: "Hospital Name",
        location: "Location",
        serialNumber: "Serial Number",
      };

      // Use selected fields or default to all fields
      const selectedFields =
        fields && fields.length > 0
          ? fields.filter((field) => field in availableFields)
          : Object.keys(availableFields);

      if (selectedFields.length === 0) {
        return res.status(400).json({ error: "No valid fields specified" });
      }

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split("T")[0];
      const deviceFilter = deviceId ? `_device_${deviceId}` : "_all_devices";
      const filename = `sensor_data${deviceFilter}_${timestamp}`;

      switch (format.toLowerCase()) {
        case "csv":
          const csvHeaders = selectedFields.map(
            (field) => availableFields[field as keyof typeof availableFields]
          );
          const csvRows = readings.map((reading) =>
            selectedFields.map((field) => {
              const value = reading[field as keyof typeof reading];
              if (field === "timestamp" && value instanceof Date) {
                return value.toISOString();
              }
              return value?.toString() || "";
            })
          );

          const csvContent = [csvHeaders, ...csvRows]
            .map((row) => row.map((cell) => `"${cell}"`).join(","))
            .join("\n");

          res.setHeader("Content-Type", "text/csv");
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${filename}.csv"`
          );
          res.send(csvContent);
          break;

        case "json":
          const jsonData = readings.map((reading) => {
            const filteredReading: any = {};
            selectedFields.forEach((field) => {
              const value = reading[field as keyof typeof reading];
              filteredReading[
                availableFields[field as keyof typeof availableFields]
              ] =
                field === "timestamp" && value instanceof Date
                  ? value.toISOString()
                  : value;
            });
            return filteredReading;
          });

          res.setHeader("Content-Type", "application/json");
          res.setHeader(
            "Content-Disposition",
            `attachment; filename="${filename}.json"`
          );
          res.json({
            exportInfo: {
              totalRecords: readings.length,
              exportDate: new Date().toISOString(),
              dateRange: {
                from: from?.toISOString() || null,
                to: to?.toISOString() || null,
              },
              deviceFilter: deviceId || "all",
              selectedFields: selectedFields.map(
                (field) =>
                  availableFields[field as keyof typeof availableFields]
              ),
            },
            data: jsonData,
          });
          break;

        default:
          return res
            .status(400)
            .json({
              error: "Unsupported export format. Supported formats: csv, json",
            });
      }
    } catch (error) {
      console.error("Export error:", error);
      res.status(500).json({ error: "Failed to export data" });
    }
  });

  const httpServer = createServer(app);

  // WebSocket server setup
  const wss = new WebSocketServer({ server: httpServer, path: "/ws" });

  const clients = new Set<WebSocket>();

  wss.on("connection", (ws) => {
    clients.add(ws);
    console.log("WebSocket client connected");

    ws.on("close", () => {
      clients.delete(ws);
      console.log("WebSocket client disconnected");
    });

    ws.on("error", (error) => {
      console.error("WebSocket error:", error);
      clients.delete(ws);
    });
  });

  // Broadcast function for real-time updates
  function broadcast(data: any) {
    const message = JSON.stringify(data);
    clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Initialize MQTT service
  async function initializeMqtt() {
    try {
      // Set the broadcast callback for MQTT service
      mqttService.setBroadcastCallback(broadcast);

      // Connect to MQTT broker
      await mqttService.connect();
      console.log("MQTT service connected successfully");

      // Subscribe to all existing device topics
      await mqttService.subscribeToDeviceTopics();
      console.log("MQTT subscriptions initialized");
    } catch (error) {
      console.error("Failed to initialize MQTT service:", error);
      console.log("Application will continue without MQTT functionality");
    }
  }

  // Initialize MQTT service (non-blocking)
  initializeMqtt();

  // Mock data simulation for demonstration
  setInterval(async () => {
    try {
      const devices = await storage.getDevices();

      for (const device of devices) {
        // Generate mock sensor reading
        const leftPressure = 95 + Math.random() * 20; // 95-115 range
        const rightPressure = 95 + Math.random() * 20;

        const leftStatus =
          leftPressure < 90
            ? "Warning"
            : leftPressure < 85
            ? "Critical"
            : "Normal";
        const rightStatus =
          rightPressure < 90
            ? "Warning"
            : rightPressure < 85
            ? "Critical"
            : "Normal";

        const reading = await storage.createSensorReading({
          deviceId: device.id,
          leftBankPressure: leftPressure.toFixed(2),
          rightBankPressure: rightPressure.toFixed(2),
          leftBankStatus: leftStatus,
          rightBankStatus: rightStatus,
          serialNumber: `SL08052024-${device.id.toString().padStart(2, "0")}`,
        });

        // Update device status
        const overallStatus =
          leftStatus === "Critical" || rightStatus === "Critical"
            ? "critical"
            : leftStatus === "Warning" || rightStatus === "Warning"
            ? "warning"
            : "normal";

        await storage.updateDevice(device.id, { status: overallStatus });

        // Create alert if needed
        if (overallStatus === "warning" || overallStatus === "critical") {
          await storage.createAlert({
            deviceId: device.id,
            alertType: "pressure_threshold",
            severity: overallStatus,
            message: `${
              overallStatus === "critical" ? "Critical" : "Low"
            } pressure detected on ${device.deviceId}`,
            isAcknowledged: false,
          });
        }

        // Broadcast update
        broadcast({
          type: "sensor_update",
          deviceId: device.id,
          reading: reading,
        });
      }
    } catch (error) {
      console.error("Error in mock data simulation:", error);
    }
  }, 30000); // Update every 30 seconds

  return httpServer;
}
