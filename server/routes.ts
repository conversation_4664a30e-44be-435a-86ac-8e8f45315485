import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import {
  insertDeviceSchema,
  insertSensorReadingSchema,
  insertAlertSchema,
} from "@shared/schema";
import { z } from "zod";
import { mqttService } from "./mqtt";

export async function registerRoutes(app: Express): Promise<Server> {
  // Device routes
  app.get("/api/devices", async (req, res) => {
    try {
      const devices = await storage.getDevices();
      res.json(devices);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch devices" });
    }
  });

  app.get("/api/devices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const device = await storage.getDevice(id);
      if (!device) {
        return res.status(404).json({ error: "Device not found" });
      }
      res.json(device);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch device" });
    }
  });

  app.post("/api/devices", async (req, res) => {
    try {
      const deviceData = insertDeviceSchema.parse(req.body);
      const device = await storage.createDevice(deviceData);

      // Subscribe to MQTT topic if device is active and has a topic
      if (device.isActive && device.mqttTopic && mqttService.isConnected()) {
        try {
          await mqttService.addDeviceSubscription(device);
          console.log(
            `Subscribed to MQTT topic for new device: ${device.deviceId}`
          );
        } catch (mqttError) {
          console.error(
            `Failed to subscribe to MQTT topic for device ${device.deviceId}:`,
            mqttError
          );
        }
      }

      res.status(201).json(device);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ error: "Invalid device data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create device" });
    }
  });

  app.put("/api/devices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const deviceData = insertDeviceSchema.partial().parse(req.body);

      // Get the old device data to handle MQTT subscription changes
      const oldDevice = await storage.getDevice(id);
      if (!oldDevice) {
        return res.status(404).json({ error: "Device not found" });
      }

      const device = await storage.updateDevice(id, deviceData);
      if (!device) {
        return res.status(404).json({ error: "Device not found" });
      }

      // Handle MQTT subscription changes
      if (mqttService.isConnected()) {
        try {
          // If MQTT topic changed, unsubscribe from old topic
          if (oldDevice.mqttTopic && oldDevice.mqttTopic !== device.mqttTopic) {
            await mqttService.removeDeviceSubscription(oldDevice);
          }

          // Subscribe to new topic if device is active and has a topic
          if (device.isActive && device.mqttTopic) {
            await mqttService.addDeviceSubscription(device);
          } else if (!device.isActive && oldDevice.mqttTopic) {
            // Unsubscribe if device is deactivated
            await mqttService.removeDeviceSubscription(oldDevice);
          }
        } catch (mqttError) {
          console.error(
            `Failed to update MQTT subscription for device ${device.deviceId}:`,
            mqttError
          );
        }
      }

      res.json(device);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ error: "Invalid device data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to update device" });
    }
  });

  app.delete("/api/devices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);

      // Get device data before deletion to handle MQTT unsubscription
      const device = await storage.getDevice(id);
      if (!device) {
        return res.status(404).json({ error: "Device not found" });
      }

      // Unsubscribe from MQTT topic before deleting
      if (device.mqttTopic && mqttService.isConnected()) {
        try {
          await mqttService.removeDeviceSubscription(device);
          console.log(
            `Unsubscribed from MQTT topic for deleted device: ${device.deviceId}`
          );
        } catch (mqttError) {
          console.error(
            `Failed to unsubscribe from MQTT topic for device ${device.deviceId}:`,
            mqttError
          );
        }
      }

      const success = await storage.deleteDevice(id);
      if (!success) {
        return res.status(404).json({ error: "Device not found" });
      }
      res.json({ message: "Device deleted successfully" });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete device" });
    }
  });

  // Sensor reading routes
  app.get("/api/sensor-readings", async (req, res) => {
    try {
      const deviceId = req.query.device_id
        ? parseInt(req.query.device_id as string)
        : undefined;
      const from = req.query.from
        ? new Date(req.query.from as string)
        : undefined;
      const to = req.query.to ? new Date(req.query.to as string) : undefined;

      const readings = await storage.getSensorReadings(deviceId, from, to);
      res.json(readings);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch sensor readings" });
    }
  });

  app.get("/api/sensor-readings/latest", async (req, res) => {
    try {
      const readings = await storage.getLatestSensorReadings();
      res.json(readings);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch latest readings" });
    }
  });

  app.post("/api/sensor-readings", async (req, res) => {
    try {
      const readingData = insertSensorReadingSchema.parse(req.body);
      const reading = await storage.createSensorReading(readingData);
      res.status(201).json(reading);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ error: "Invalid reading data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create sensor reading" });
    }
  });

  // Alert routes
  app.get("/api/alerts", async (req, res) => {
    try {
      const deviceId = req.query.device_id
        ? parseInt(req.query.device_id as string)
        : undefined;
      const alerts = await storage.getAlerts(deviceId);
      res.json(alerts);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch alerts" });
    }
  });

  app.get("/api/alerts/unacknowledged", async (req, res) => {
    try {
      const alerts = await storage.getUnacknowledgedAlerts();
      res.json(alerts);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch unacknowledged alerts" });
    }
  });

  app.post("/api/alerts", async (req, res) => {
    try {
      const alertData = insertAlertSchema.parse(req.body);
      const alert = await storage.createAlert(alertData);
      res.status(201).json(alert);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res
          .status(400)
          .json({ error: "Invalid alert data", details: error.errors });
      }
      res.status(500).json({ error: "Failed to create alert" });
    }
  });

  app.patch("/api/alerts/:id/acknowledge", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.acknowledgeAlert(id);
      if (!success) {
        return res.status(404).json({ error: "Alert not found" });
      }
      res.json({ message: "Alert acknowledged successfully" });
    } catch (error) {
      res.status(500).json({ error: "Failed to acknowledge alert" });
    }
  });

  // MQTT status route
  app.get("/api/mqtt/status", async (req, res) => {
    try {
      const status = {
        connected: mqttService.isConnected(),
        subscribedTopics: mqttService.getSubscribedTopics(),
        config: {
          host: process.env.MQTT_HOST || "localhost",
          port: process.env.MQTT_PORT || "1883",
          clientId: process.env.MQTT_CLIENT_ID || "smart_hospital_monitor",
        },
      };
      res.json(status);
    } catch (error) {
      res.status(500).json({ error: "Failed to get MQTT status" });
    }
  });

  // Export route
  app.get("/api/export", async (req, res) => {
    try {
      const deviceId = req.query.device_id
        ? parseInt(req.query.device_id as string)
        : undefined;
      const from = req.query.from
        ? new Date(req.query.from as string)
        : undefined;
      const to = req.query.to ? new Date(req.query.to as string) : undefined;

      const readings = await storage.getSensorReadings(deviceId, from, to);

      // Convert to CSV
      const csvHeaders = [
        "Device ID",
        "Timestamp",
        "Left Bank Pressure",
        "Right Bank Pressure",
        "Left Bank Status",
        "Right Bank Status",
        "Serial Number",
      ];

      const csvRows = readings.map((reading) => [
        reading.deviceId,
        reading.timestamp.toISOString(),
        reading.leftBankPressure,
        reading.rightBankPressure,
        reading.leftBankStatus,
        reading.rightBankStatus,
        reading.serialNumber,
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map((row) => row.map((cell) => `"${cell || ""}"`).join(","))
        .join("\n");

      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        'attachment; filename="sensor_data.csv"'
      );
      res.send(csvContent);
    } catch (error) {
      res.status(500).json({ error: "Failed to export data" });
    }
  });

  const httpServer = createServer(app);

  // WebSocket server setup
  const wss = new WebSocketServer({ server: httpServer, path: "/ws" });

  const clients = new Set<WebSocket>();

  wss.on("connection", (ws) => {
    clients.add(ws);
    console.log("WebSocket client connected");

    ws.on("close", () => {
      clients.delete(ws);
      console.log("WebSocket client disconnected");
    });

    ws.on("error", (error) => {
      console.error("WebSocket error:", error);
      clients.delete(ws);
    });
  });

  // Broadcast function for real-time updates
  function broadcast(data: any) {
    const message = JSON.stringify(data);
    clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Initialize MQTT service
  async function initializeMqtt() {
    try {
      // Set the broadcast callback for MQTT service
      mqttService.setBroadcastCallback(broadcast);

      // Connect to MQTT broker
      await mqttService.connect();
      console.log("MQTT service connected successfully");

      // Subscribe to all existing device topics
      await mqttService.subscribeToDeviceTopics();
      console.log("MQTT subscriptions initialized");
    } catch (error) {
      console.error("Failed to initialize MQTT service:", error);
      console.log("Application will continue without MQTT functionality");
    }
  }

  // Initialize MQTT service (non-blocking)
  initializeMqtt();

  // Mock data simulation for demonstration
  setInterval(async () => {
    try {
      const devices = await storage.getDevices();

      for (const device of devices) {
        // Generate mock sensor reading
        const leftPressure = 95 + Math.random() * 20; // 95-115 range
        const rightPressure = 95 + Math.random() * 20;

        const leftStatus =
          leftPressure < 90
            ? "Warning"
            : leftPressure < 85
            ? "Critical"
            : "Normal";
        const rightStatus =
          rightPressure < 90
            ? "Warning"
            : rightPressure < 85
            ? "Critical"
            : "Normal";

        const reading = await storage.createSensorReading({
          deviceId: device.id,
          leftBankPressure: leftPressure.toFixed(2),
          rightBankPressure: rightPressure.toFixed(2),
          leftBankStatus: leftStatus,
          rightBankStatus: rightStatus,
          serialNumber: `SL08052024-${device.id.toString().padStart(2, "0")}`,
        });

        // Update device status
        const overallStatus =
          leftStatus === "Critical" || rightStatus === "Critical"
            ? "critical"
            : leftStatus === "Warning" || rightStatus === "Warning"
            ? "warning"
            : "normal";

        await storage.updateDevice(device.id, { status: overallStatus });

        // Create alert if needed
        if (overallStatus === "warning" || overallStatus === "critical") {
          await storage.createAlert({
            deviceId: device.id,
            alertType: "pressure_threshold",
            severity: overallStatus,
            message: `${
              overallStatus === "critical" ? "Critical" : "Low"
            } pressure detected on ${device.deviceId}`,
            isAcknowledged: false,
          });
        }

        // Broadcast update
        broadcast({
          type: "sensor_update",
          deviceId: device.id,
          reading: reading,
        });
      }
    } catch (error) {
      console.error("Error in mock data simulation:", error);
    }
  }, 30000); // Update every 30 seconds

  return httpServer;
}
