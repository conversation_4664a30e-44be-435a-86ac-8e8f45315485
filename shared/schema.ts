import {
  pgTable,
  text,
  serial,
  integer,
  boolean,
  timestamp,
  decimal,
  varchar,
  index,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const devices = pgTable("devices", {
  id: serial("id").primaryKey(),
  deviceId: varchar("device_id", { length: 50 }).notNull().unique(),
  deviceType: varchar("device_type", { length: 100 }).notNull(),
  mqttTopic: varchar("mqtt_topic", { length: 200 }).notNull(),
  hospital: varchar("hospital", { length: 100 }).notNull(),
  location: varchar("location", { length: 200 }).notNull(),
  status: varchar("status", { length: 50 }).notNull().default("normal"),
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const sensorReadings = pgTable(
  "sensor_readings",
  {
    id: serial("id").primaryKey(),
    deviceId: integer("device_id")
      .references(() => devices.id)
      .notNull(),
    // Pressure readings
    leftBankPressure: decimal("left_bank_pressure", { precision: 8, scale: 2 }),
    rightBankPressure: decimal("right_bank_pressure", {
      precision: 8,
      scale: 2,
    }),
    // Status readings
    leftBankStatus: varchar("left_bank_status", { length: 50 }),
    rightBankStatus: varchar("right_bank_status", { length: 50 }),
    // Additional MQTT fields for comprehensive monitoring
    leftBankCapacity: decimal("left_bank_capacity", { precision: 8, scale: 2 }),
    rightBankCapacity: decimal("right_bank_capacity", {
      precision: 8,
      scale: 2,
    }),
    leftBankPercentage: decimal("left_bank_percentage", {
      precision: 5,
      scale: 2,
    }),
    rightBankPercentage: decimal("right_bank_percentage", {
      precision: 5,
      scale: 2,
    }),
    leftBankSourceType: varchar("left_bank_source_type", { length: 50 }),
    rightBankSourceType: varchar("right_bank_source_type", { length: 50 }),
    hospitalName: varchar("hospital_name", { length: 100 }),
    location: varchar("location", { length: 200 }),
    serialNumber: varchar("serial_number", { length: 100 }),
    timestamp: timestamp("timestamp").defaultNow().notNull(),
  },
  (table) => ({
    // Index for device-specific queries (most common query pattern)
    deviceIdIdx: index("sensor_readings_device_id_idx").on(table.deviceId),
    // Index for timestamp ordering and filtering (real-time data retrieval)
    timestampIdx: index("sensor_readings_timestamp_idx").on(table.timestamp),
    // Composite index for device + timestamp queries (optimal for device-specific time-series data)
    deviceTimestampIdx: index("sensor_readings_device_timestamp_idx").on(
      table.deviceId,
      table.timestamp
    ),
  })
);

export const alerts = pgTable(
  "alerts",
  {
    id: serial("id").primaryKey(),
    deviceId: integer("device_id")
      .references(() => devices.id)
      .notNull(),
    alertType: varchar("alert_type", { length: 50 }).notNull(),
    severity: varchar("severity", { length: 20 }).notNull(),
    message: text("message").notNull(),
    isAcknowledged: boolean("is_acknowledged").notNull().default(false),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    acknowledgedAt: timestamp("acknowledged_at"),
  },
  (table) => ({
    // Index for device-specific alert queries
    deviceIdIdx: index("alerts_device_id_idx").on(table.deviceId),
    // Index for chronological ordering (most recent alerts first)
    createdAtIdx: index("alerts_created_at_idx").on(table.createdAt),
    // Index for filtering unacknowledged alerts (dashboard notifications)
    isAcknowledgedIdx: index("alerts_is_acknowledged_idx").on(
      table.isAcknowledged
    ),
    // Composite index for device + acknowledgment status queries
    deviceAcknowledgedIdx: index("alerts_device_acknowledged_idx").on(
      table.deviceId,
      table.isAcknowledged
    ),
  })
);

export const devicesRelations = relations(devices, ({ many }) => ({
  sensorReadings: many(sensorReadings),
  alerts: many(alerts),
}));

export const sensorReadingsRelations = relations(sensorReadings, ({ one }) => ({
  device: one(devices, {
    fields: [sensorReadings.deviceId],
    references: [devices.id],
  }),
}));

export const alertsRelations = relations(alerts, ({ one }) => ({
  device: one(devices, {
    fields: [alerts.deviceId],
    references: [devices.id],
  }),
}));

export const insertDeviceSchema = createInsertSchema(devices).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertSensorReadingSchema = createInsertSchema(
  sensorReadings
).omit({
  id: true,
  timestamp: true,
});

export const insertAlertSchema = createInsertSchema(alerts).omit({
  id: true,
  createdAt: true,
  acknowledgedAt: true,
});

export type Device = typeof devices.$inferSelect;
export type InsertDevice = z.infer<typeof insertDeviceSchema>;
export type SensorReading = typeof sensorReadings.$inferSelect;
export type InsertSensorReading = z.infer<typeof insertSensorReadingSchema>;
export type Alert = typeof alerts.$inferSelect;
export type InsertAlert = z.infer<typeof insertAlertSchema>;

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
