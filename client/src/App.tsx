import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { NordToaster } from "@/components/nord-toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";
import Dashboard from "@/pages/nord-dashboard";
import DeviceManagement from "@/pages/nord-device-management";
import Analytics from "@/pages/nord-analytics";
import Alerts from "@/pages/nord-alerts";
import Export from "@/pages/nord-export";
import DeviceSummary from "@/pages/device-summary";
import { NordLayout } from "@/components/nord-layout";

// Page metadata
const pageMetadata = {
  "/": {
    title: "Dashboard",
    description: "Real-time monitoring of hospital devices",
  },
  "/devices": {
    title: "Device Management",
    description: "Configure and manage medical devices",
  },
  "/analytics": {
    title: "Analytics",
    description: "Analyze device performance and trends",
  },
  "/alerts": { title: "Alerts", description: "View and manage system alerts" },
  "/export": {
    title: "Export Data",
    description: "Export device data and reports",
  },
};

function Router() {
  const [location] = useLocation();
  const metadata = pageMetadata[location as keyof typeof pageMetadata] || {};

  return (
    <NordLayout title={metadata.title} description={metadata.description}>
      <Switch>
        <Route path="/" component={Dashboard} />
        <Route path="/devices" component={DeviceManagement} />
        <Route path="/analytics" component={Analytics} />
        <Route path="/alerts" component={Alerts} />
        <Route path="/export" component={Export} />
        <Route path="/device/:deviceId" component={DeviceSummary} />
        <Route component={NotFound} />
      </Switch>
    </NordLayout>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <NordToaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
