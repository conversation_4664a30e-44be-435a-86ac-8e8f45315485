import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  hospital: string;
  location: string;
  status: string;
}

interface SensorReading {
  id: number;
  deviceId: number;
  leftBankPressure: string;
  rightBankPressure: string;
  leftBankStatus: string;
  rightBankStatus: string;
  timestamp: string;
}

export default function Analytics() {
  const [selectedDevice, setSelectedDevice] = useState<string>("all");
  const [fromDate, setFromDate] = useState<string>("");
  const [toDate, setToDate] = useState<string>("");

  const { data: devices = [] } = useQuery<Device[]>({
    queryKey: ["/api/devices"],
  });

  const { data: readings = [] } = useQuery<SensorReading[]>({
    queryKey: ["/api/sensor-readings", selectedDevice, fromDate, toDate],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (selectedDevice !== "all") {
        params.append("device_id", selectedDevice);
      }
      if (fromDate) {
        params.append("from", fromDate);
      }
      if (toDate) {
        params.append("to", toDate);
      }

      const response = await fetch(`/api/sensor-readings?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch sensor readings");
      }
      return response.json();
    },
  });

  // Process data for charts
  const chartData = readings
    .slice(0, 50)
    .reverse()
    .map((reading) => ({
      timestamp: new Date(reading.timestamp).toLocaleTimeString(),
      leftBank: parseFloat(reading.leftBankPressure),
      rightBank: parseFloat(reading.rightBankPressure),
    }));

  const statusData = devices.reduce((acc, device) => {
    const existing = acc.find((item) => item.name === device.status);
    if (existing) {
      existing.value += 1;
    } else {
      acc.push({ name: device.status, value: 1 });
    }
    return acc;
  }, [] as { name: string; value: number }[]);

  const statusColors = {
    normal: "#10B981",
    warning: "#F59E0B",
    critical: "#EF4444",
    offline: "#64748B",
  };

  const recentActivity = readings.slice(0, 10).map((reading) => {
    const device = devices.find((d) => d.id === reading.deviceId);
    return {
      ...reading,
      device: device?.deviceId || "Unknown",
      location: device?.location || "Unknown",
    };
  });

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-text-primary">
              Pressure Trends
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Real-time pressure monitoring and historical analysis
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Select value={selectedDevice} onValueChange={setSelectedDevice}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select device" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Devices</SelectItem>
                {devices.map((device) => (
                  <SelectItem key={device.id} value={device.id.toString()}>
                    {device.deviceId}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex items-center space-x-2">
              <Input
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
                className="w-auto"
              />
              <span className="text-text-secondary">to</span>
              <Input
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
                className="w-auto"
              />
            </div>
          </div>
        </div>

        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis
                domain={[90, 120]}
                label={{
                  value: "Pressure (Kgcm²)",
                  angle: -90,
                  position: "insideLeft",
                }}
              />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="leftBank"
                stroke="#2563EB"
                name="Left Bank Pressure"
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="rightBank"
                stroke="#10B981"
                name="Right Bank Pressure"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">
            Device Status Distribution
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={
                        statusColors[entry.name as keyof typeof statusColors] ||
                        "#64748B"
                      }
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">
            Recent Activity
          </h3>
          <div className="space-y-3">
            {recentActivity.map((reading) => (
              <div
                key={reading.id}
                className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
              >
                <div className="w-2 h-2 bg-medical-blue rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-text-primary">
                    {reading.device} - Data Update
                  </p>
                  <p className="text-xs text-text-secondary">
                    L: {reading.leftBankPressure} Kgcm², R:{" "}
                    {reading.rightBankPressure} Kgcm²
                  </p>
                </div>
                <span className="text-xs text-text-secondary">
                  {new Date(reading.timestamp).toLocaleTimeString()}
                </span>
              </div>
            ))}

            {recentActivity.length === 0 && (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="fas fa-chart-line text-gray-400 text-lg"></i>
                </div>
                <p className="text-text-secondary">
                  No recent activity data available
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
