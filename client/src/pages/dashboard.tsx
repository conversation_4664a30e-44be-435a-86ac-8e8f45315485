import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { NordDeviceCard } from "@/components/nord-device-card";
import { connectWebSocket } from "@/lib/websocket";
import { queryClient } from "@/lib/queryClient";

interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  mqttTopic: string;
  hospital: string;
  location: string;
  status: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface SensorReading {
  id: number;
  deviceId: number;
  leftBankPressure: string;
  rightBankPressure: string;
  leftBankStatus: string;
  rightBankStatus: string;
  serialNumber: string;
  timestamp: string;
}

export default function Dashboard() {
  const { data: devices = [], isLoading } = useQuery<Device[]>({
    queryKey: ["/api/devices"],
  });

  const { data: readings = [] } = useQuery<SensorReading[]>({
    queryKey: ["/api/sensor-readings/latest"],
  });

  useEffect(() => {
    const cleanup = connectWebSocket((data) => {
      if (data.type === "sensor_update") {
        queryClient.invalidateQueries({ queryKey: ["/api/devices"] });
        queryClient.invalidateQueries({
          queryKey: ["/api/sensor-readings/latest"],
        });
      }
    });

    return cleanup;
  }, []);

  const getDeviceReading = (deviceId: number) => {
    return readings.find((r) => r.deviceId === deviceId);
  };

  const getStatusCounts = () => {
    const total = devices.length;
    const active = devices.filter((d) => d.status === "normal").length;
    const warnings = devices.filter((d) => d.status === "warning").length;
    const critical = devices.filter((d) => d.status === "critical").length;

    return { total, active, warnings, critical };
  };

  const statusCounts = getStatusCounts();

  if (isLoading) {
    return (
      <nord-stack gap="l" direction="vertical">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <nord-card key={i} padding="l">
              <nord-skeleton animate lines={3}></nord-skeleton>
            </nord-card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <nord-card key={i} padding="l">
              <nord-skeleton animate lines={5}></nord-skeleton>
            </nord-card>
          ))}
        </div>
      </nord-stack>
    );
  }

  return (
    <nord-stack gap="l" direction="vertical">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Total Devices
            </p>
            <nord-stack gap="m" align="center" justify="space-between">
              <span
                style={{
                  fontSize: "2rem",
                  fontWeight: "bold",
                  color: "var(--n-color-text)",
                }}
              >
                {statusCounts.total}
              </span>
              <nord-icon
                name="interface-content-tree"
                size="l"
                color="var(--n-color-accent)"
              ></nord-icon>
            </nord-stack>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Active Devices
            </p>
            <nord-stack gap="m" align="center" justify="space-between">
              <span
                style={{
                  fontSize: "2rem",
                  fontWeight: "bold",
                  color: "var(--n-color-status-success)",
                }}
              >
                {statusCounts.active}
              </span>
              <nord-icon
                name="interface-checked"
                size="l"
                color="var(--n-color-status-success)"
              ></nord-icon>
            </nord-stack>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Warnings
            </p>
            <nord-stack gap="m" align="center" justify="space-between">
              <span
                style={{
                  fontSize: "2rem",
                  fontWeight: "bold",
                  color: "var(--n-color-status-warning)",
                }}
              >
                {statusCounts.warnings}
              </span>
              <nord-icon
                name="navigation-warning"
                size="l"
                color="var(--n-color-status-warning)"
              ></nord-icon>
            </nord-stack>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Critical Alerts
            </p>
            <nord-stack gap="m" align="center" justify="space-between">
              <span
                style={{
                  fontSize: "2rem",
                  fontWeight: "bold",
                  color: "var(--n-color-status-danger)",
                }}
              >
                {statusCounts.critical}
              </span>
              <nord-icon
                name="navigation-close-circle"
                size="l"
                color="var(--n-color-status-danger)"
              ></nord-icon>
            </nord-stack>
          </nord-stack>
        </nord-card>
      </div>

      {/* Device Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {devices.map((device) => (
          <NordDeviceCard
            key={device.id}
            device={device}
            reading={getDeviceReading(device.id)}
          />
        ))}
      </div>

      {devices.length === 0 && (
        <nord-empty-state>
          <nord-icon
            slot="icon"
            name="interface-content-tree"
            size="xl"
          ></nord-icon>
          <h3 slot="header">No Devices Found</h3>
          <p slot="description">Add your first device to start monitoring.</p>
          <nord-button slot="action" href="/devices">
            Add Device
          </nord-button>
        </nord-empty-state>
      )}
    </nord-stack>
  );
}
