import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { Download, Eye } from "lucide-react";

interface Device {
  id: number;
  deviceId: string;
  location: string;
}

export default function Export() {
  const [selectedDevice, setSelectedDevice] = useState<string>("all");
  const [fromDate, setFromDate] = useState<string>("");
  const [toDate, setToDate] = useState<string>("");
  const [exportFormat, setExportFormat] = useState<string>("csv");
  const [selectedFields, setSelectedFields] = useState<string[]>([
    "deviceId",
    "timestamp",
    "leftBankPressure",
    "rightBankPressure",
    "leftBankStatus",
    "rightBankStatus",
    "location",
  ]);
  const { toast } = useToast();

  const { data: devices = [] } = useQuery<Device[]>({
    queryKey: ["/api/devices"],
  });

  // Updated data fields to match the actual sensor reading schema
  const dataFields = [
    { id: "deviceId", label: "Device ID" },
    { id: "timestamp", label: "Timestamp" },
    { id: "leftBankPressure", label: "Left Bank Pressure (Kgcm2)" },
    { id: "rightBankPressure", label: "Right Bank Pressure (Kgcm2)" },
    { id: "leftBankStatus", label: "Left Bank Status" },
    { id: "rightBankStatus", label: "Right Bank Status" },
    { id: "leftBankCapacity", label: "Left Bank Capacity (Kgcm2)" },
    { id: "rightBankCapacity", label: "Right Bank Capacity (Kgcm2)" },
    { id: "leftBankPercentage", label: "Left Bank Percentage (%)" },
    { id: "rightBankPercentage", label: "Right Bank Percentage (%)" },
    { id: "leftBankSourceType", label: "Left Bank Source Type" },
    { id: "rightBankSourceType", label: "Right Bank Source Type" },
    { id: "hospitalName", label: "Hospital Name" },
    { id: "location", label: "Location" },
    { id: "serialNumber", label: "Serial Number" },
  ];

  const handleFieldChange = (fieldId: string, checked: boolean) => {
    setSelectedFields((prev) =>
      checked ? [...prev, fieldId] : prev.filter((id) => id !== fieldId)
    );
  };

  const handleExportData = async () => {
    try {
      // Validate inputs
      if (selectedFields.length === 0) {
        toast({
          title: "Export failed",
          description: "Please select at least one field to export.",
          variant: "destructive",
        });
        return;
      }

      if (fromDate && toDate && new Date(fromDate) > new Date(toDate)) {
        toast({
          title: "Export failed",
          description: "From date must be before to date.",
          variant: "destructive",
        });
        return;
      }

      const params = new URLSearchParams();
      if (selectedDevice !== "all") {
        params.append("device_id", selectedDevice);
      }
      if (fromDate) {
        params.append("from", fromDate);
      }
      if (toDate) {
        params.append("to", toDate);
      }
      params.append("format", exportFormat);
      params.append("fields", selectedFields.join(","));

      const response = await fetch(`/api/export?${params}`);

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ error: "Export failed" }));
        throw new Error(errorData.error || "Export failed");
      }

      // Handle different response types based on format
      if (exportFormat === "json") {
        const jsonData = await response.json();
        const blob = new Blob([JSON.stringify(jsonData, null, 2)], {
          type: "application/json",
        });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download =
          response.headers
            .get("Content-Disposition")
            ?.split("filename=")[1]
            ?.replace(/"/g, "") ||
          `sensor_data_${new Date().toISOString().split("T")[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        // Handle CSV and other blob formats
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download =
          response.headers
            .get("Content-Disposition")
            ?.split("filename=")[1]
            ?.replace(/"/g, "") ||
          `sensor_data_${
            new Date().toISOString().split("T")[0]
          }.${exportFormat}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }

      toast({
        title: "Export successful",
        description: `Your data has been exported successfully as ${exportFormat.toUpperCase()}.`,
      });
    } catch (error) {
      console.error("Export error:", error);
      toast({
        title: "Export failed",
        description:
          error instanceof Error
            ? error.message
            : "There was an error exporting your data. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePreviewData = async () => {
    try {
      const params = new URLSearchParams();
      if (selectedDevice !== "all") {
        params.append("device_id", selectedDevice);
      }
      if (fromDate) {
        params.append("from", fromDate);
      }
      if (toDate) {
        params.append("to", toDate);
      }

      const response = await fetch(`/api/sensor-readings?${params}`);
      if (!response.ok) {
        throw new Error("Preview failed");
      }

      const data = await response.json();

      toast({
        title: "Preview ready",
        description: `Found ${data.length} records matching your criteria.`,
      });
    } catch (error) {
      toast({
        title: "Preview failed",
        description:
          "There was an error previewing your data. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-text-primary">
            Export Data
          </h2>
        </div>

        <div className="p-6">
          <div className="max-w-2xl space-y-6">
            <div>
              <Label
                htmlFor="device-select"
                className="text-sm font-medium text-text-primary"
              >
                Select Device
              </Label>
              <Select value={selectedDevice} onValueChange={setSelectedDevice}>
                <SelectTrigger className="w-full mt-2">
                  <SelectValue placeholder="Select device" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Devices</SelectItem>
                  {devices.map((device) => (
                    <SelectItem key={device.id} value={device.id.toString()}>
                      {device.deviceId} - {device.location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label
                  htmlFor="from-date"
                  className="text-sm font-medium text-text-primary"
                >
                  From Date
                </Label>
                <Input
                  id="from-date"
                  type="date"
                  value={fromDate}
                  onChange={(e) => setFromDate(e.target.value)}
                  className="mt-2"
                />
              </div>
              <div>
                <Label
                  htmlFor="to-date"
                  className="text-sm font-medium text-text-primary"
                >
                  To Date
                </Label>
                <Input
                  id="to-date"
                  type="date"
                  value={toDate}
                  onChange={(e) => setToDate(e.target.value)}
                  className="mt-2"
                />
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-text-primary">
                Export Format
              </Label>
              <div className="flex space-x-6 mt-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="format"
                    value="csv"
                    checked={exportFormat === "csv"}
                    onChange={(e) => setExportFormat(e.target.value)}
                    className="text-medical-blue focus:ring-medical-blue"
                  />
                  <span className="text-sm text-text-primary">CSV</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="format"
                    value="json"
                    checked={exportFormat === "json"}
                    onChange={(e) => setExportFormat(e.target.value)}
                    className="text-medical-blue focus:ring-medical-blue"
                  />
                  <span className="text-sm text-text-primary">JSON</span>
                </label>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                CSV format is ideal for spreadsheet applications. JSON format
                includes metadata and is suitable for data analysis.
              </p>
            </div>

            <div>
              <Label className="text-sm font-medium text-text-primary">
                Data Fields
              </Label>
              <div className="flex items-center space-x-4 mt-2 mb-3">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedFields(dataFields.map((f) => f.id))}
                  className="text-xs"
                >
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedFields([])}
                  className="text-xs"
                >
                  Clear All
                </Button>
                <span className="text-xs text-gray-500">
                  {selectedFields.length} of {dataFields.length} fields selected
                </span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto border rounded-md p-3 bg-gray-50">
                {dataFields.map((field) => (
                  <div key={field.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={field.id}
                      checked={selectedFields.includes(field.id)}
                      onCheckedChange={(checked) =>
                        handleFieldChange(field.id, checked as boolean)
                      }
                      className="text-medical-blue focus:ring-medical-blue"
                    />
                    <Label
                      htmlFor={field.id}
                      className="text-sm text-text-primary cursor-pointer"
                    >
                      {field.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-4 pt-4">
              <Button
                onClick={handleExportData}
                className="bg-medical-blue hover:bg-blue-600"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button variant="outline" onClick={handlePreviewData}>
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
