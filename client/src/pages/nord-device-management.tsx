import { useQuery, useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  mqttTopic: string;
  hospital: string;
  location: string;
  status: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function NordDeviceManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showAddModal, setShowAddModal] = useState(false);
  const { toast } = useToast();

  const { data: devices = [], isLoading } = useQuery<Device[]>({
    queryKey: ["/api/devices"],
  });

  const deleteDeviceMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/devices/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/devices"] });
      toast({
        title: "Device deleted",
        description: "The device has been successfully deleted.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete device. Please try again.",
        variant: "destructive",
      });
    },
  });

  const filteredDevices = devices.filter((device) => {
    const matchesSearch =
      device.deviceId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      device.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || device.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "normal":
        return "success";
      case "warning":
        return "warning";
      case "critical":
        return "danger";
      default:
        return "neutral";
    }
  };

  const handleDeleteDevice = (id: number) => {
    // Nord doesn't have a built-in confirm dialog, so we'll use a simple confirm for now
    if (window.confirm("Are you sure you want to delete this device?")) {
      deleteDeviceMutation.mutate(id);
    }
  };

  if (isLoading) {
    return (
      <nord-card padding="l">
        <nord-skeleton animate lines={10}></nord-skeleton>
      </nord-card>
    );
  }

  return (
    <nord-stack gap="l" direction="vertical">
      {/* Header and Actions */}
      <nord-card padding="l">
        <nord-stack gap="m" align="center" justify="space-between">
          <h2
            className="text-xl font-semibold"
            style={{ color: "var(--n-color-text)" }}
          >
            Device Management
          </h2>
          <nord-button variant="primary" onClick={() => setShowAddModal(true)}>
            <nord-icon slot="start" name="interface-add"></nord-icon>
            Add New Device
          </nord-button>
        </nord-stack>
      </nord-card>

      {/* Filters */}
      <nord-card padding="l">
        <nord-stack gap="m" wrap>
          <nord-input
            placeholder="Search devices..."
            value={searchTerm}
            onInput={(e: any) => setSearchTerm(e.target.value)}
            style={{ flex: "1", minWidth: "300px" }}
          >
            <nord-icon slot="start" name="interface-search"></nord-icon>
          </nord-input>

          <nord-select
            value={statusFilter}
            onInput={(e: any) => setStatusFilter(e.target.value)}
            style={{ minWidth: "200px" }}
          >
            <option value="all">All Status</option>
            <option value="normal">Normal</option>
            <option value="warning">Warning</option>
            <option value="critical">Critical</option>
          </nord-select>
        </nord-stack>
      </nord-card>

      {/* Device Table */}
      <nord-card padding="none">
        <nord-table>
          <table>
            <thead>
              <tr>
                <th>Device</th>
                <th>Type</th>
                <th>Location</th>
                <th>Status</th>
                <th>Last Updated</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredDevices.map((device) => (
                <tr key={device.id}>
                  <td>
                    <nord-stack gap="s" align="center">
                      <nord-icon
                        name="medical-device"
                        color="var(--n-color-accent)"
                      ></nord-icon>
                      <div>
                        <div style={{ fontWeight: "500" }}>
                          {device.deviceId}
                        </div>
                        <div
                          style={{
                            fontSize: "0.875rem",
                            color: "var(--n-color-text-weak)",
                          }}
                        >
                          {device.hospital}
                        </div>
                      </div>
                    </nord-stack>
                  </td>
                  <td>{device.deviceType}</td>
                  <td>{device.location}</td>
                  <td>
                    <nord-badge variant={getStatusVariant(device.status)}>
                      {device.status}
                    </nord-badge>
                  </td>
                  <td>{new Date(device.updatedAt).toLocaleString()}</td>
                  <td>
                    <nord-stack gap="s">
                      <nord-button size="s" variant="plain">
                        <nord-icon name="interface-edit"></nord-icon>
                      </nord-button>
                      <nord-button
                        size="s"
                        variant="plain"
                        onClick={() => handleDeleteDevice(device.id)}
                      >
                        <nord-icon
                          name="interface-delete"
                          color="var(--n-color-status-danger)"
                        ></nord-icon>
                      </nord-button>
                    </nord-stack>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </nord-table>

        {filteredDevices.length === 0 && (
          <div style={{ padding: "3rem", textAlign: "center" }}>
            <nord-empty-state>
              <nord-icon
                slot="icon"
                name="interface-search"
                size="xl"
              ></nord-icon>
              <h3 slot="header">No devices found</h3>
              <p slot="description">
                {searchTerm || statusFilter !== "all"
                  ? "Try adjusting your search or filters"
                  : "Add your first device to get started"}
              </p>
            </nord-empty-state>
          </div>
        )}
      </nord-card>

      {/* Add Device Modal - Nord Modal implementation */}
      {showAddModal && (
        <nord-modal open={showAddModal} onClose={() => setShowAddModal(false)}>
          <h2 slot="header">Add New Device</h2>
          <nord-stack gap="m" direction="vertical">
            <nord-input label="Device ID" required></nord-input>
            <nord-input label="Device Type" required></nord-input>
            <nord-input label="Hospital" required></nord-input>
            <nord-input label="Location" required></nord-input>
            <nord-select label="Status">
              <option value="normal">Normal</option>
              <option value="warning">Warning</option>
              <option value="critical">Critical</option>
            </nord-select>
          </nord-stack>
          <nord-button-group slot="footer">
            <nord-button onClick={() => setShowAddModal(false)}>
              Cancel
            </nord-button>
            <nord-button variant="primary">Add Device</nord-button>
          </nord-button-group>
        </nord-modal>
      )}
    </nord-stack>
  );
}
