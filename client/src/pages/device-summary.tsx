import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useRoute, useLocation } from "wouter";
import { connectWebSocket } from "@/lib/websocket";
import { queryClient } from "@/lib/queryClient";
import { RealTimeSensorDisplay } from "@/components/real-time-sensor-display";
import { RealTimeDeviceStatus } from "@/components/real-time-device-status";

interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  mqttTopic: string;
  hospital: string;
  location: string;
  status: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface SensorReading {
  id: number;
  deviceId: number;
  leftBankPressure: string;
  rightBankPressure: string;
  leftBankStatus: string;
  rightBankStatus: string;
  leftBankCapacity?: string;
  rightBankCapacity?: string;
  leftBankPercentage?: string;
  rightBankPercentage?: string;
  leftBankSourceType?: string;
  rightBankSourceType?: string;
  hospitalName?: string;
  location?: string;
  serialNumber: string;
  timestamp: string;
}

interface Alert {
  id: number;
  deviceId: number;
  alertType: string;
  severity: string;
  message: string;
  isAcknowledged: boolean;
  createdAt: string;
}

export default function DeviceSummary() {
  const [, params] = useRoute("/device/:deviceId");
  const [, setLocation] = useLocation();
  const deviceId = params?.deviceId;

  const { data: devices = [] } = useQuery<Device[]>({
    queryKey: ["/api/devices"],
  });

  const { data: readings = [] } = useQuery<SensorReading[]>({
    queryKey: ["/api/sensor-readings"],
    enabled: !!deviceId,
  });

  const { data: alerts = [] } = useQuery<Alert[]>({
    queryKey: ["/api/alerts"],
    enabled: !!deviceId,
  });

  useEffect(() => {
    const cleanup = connectWebSocket((data) => {
      if (data.type === "sensor_update") {
        queryClient.invalidateQueries({ queryKey: ["/api/sensor-readings"] });
        queryClient.invalidateQueries({ queryKey: ["/api/devices"] });
        queryClient.invalidateQueries({ queryKey: ["/api/alerts"] });
      }
    });

    return cleanup;
  }, []);

  if (!deviceId) {
    return (
      <nord-empty-state>
        <nord-icon slot="icon" name="navigation-warning" size="xl"></nord-icon>
        <h3 slot="header">Invalid Device</h3>
        <p slot="description">No device ID provided in the URL.</p>
        <nord-button slot="action" onClick={() => setLocation("/")}>
          Back to Dashboard
        </nord-button>
      </nord-empty-state>
    );
  }

  const device = devices.find((d) => d.deviceId === deviceId);
  const deviceReadings = readings.filter((r) => r.deviceId === device?.id);
  const deviceAlerts = alerts.filter((a) => a.deviceId === device?.id);
  const latestReading = deviceReadings[0];

  if (!device) {
    return (
      <nord-empty-state>
        <nord-icon
          slot="icon"
          name="interface-content-tree"
          size="xl"
        ></nord-icon>
        <h3 slot="header">Device Not Found</h3>
        <p slot="description">
          Device with ID "{deviceId}" could not be found.
        </p>
        <nord-button slot="action" onClick={() => setLocation("/")}>
          Back to Dashboard
        </nord-button>
      </nord-empty-state>
    );
  }

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "normal":
      case "online":
        return "success";
      case "warning":
        return "warning";
      case "critical":
      case "offline":
        return "danger";
      default:
        return "neutral";
    }
  };

  return (
    <nord-stack gap="l" direction="vertical">
      {/* Header */}
      <nord-stack gap="m" align="center" justify="space-between">
        <nord-stack gap="m" align="center">
          <nord-button
            variant="plain"
            size="s"
            onClick={() => setLocation("/")}
          >
            <nord-icon name="navigation-arrow-left" slot="start"></nord-icon>
            Back to Dashboard
          </nord-button>
          <nord-divider direction="vertical"></nord-divider>
          <h1 style={{ color: "var(--n-color-text)", margin: 0 }}>
            Device {device.deviceId}
          </h1>
          <nord-badge variant={getStatusVariant(device.status)}>
            {device.status}
          </nord-badge>
        </nord-stack>
      </nord-stack>

      {/* Device Status */}
      <RealTimeDeviceStatus device={device} showDetails={true} />

      {/* Real-time Sensor Data */}
      <RealTimeSensorDisplay
        deviceId={device.id}
        initialReading={latestReading}
        showExtendedData={true}
      />

      {/* Recent Alerts */}
      <nord-card padding="l">
        <h2 slot="header" style={{ color: "var(--n-color-text)" }}>
          Recent Alerts
        </h2>
        {deviceAlerts.length > 0 ? (
          <nord-stack gap="m" direction="vertical">
            {deviceAlerts.slice(0, 5).map((alert) => (
              <nord-card key={alert.id} padding="m" variant="flat">
                <nord-stack gap="m" align="center" justify="space-between">
                  <nord-stack gap="s" direction="vertical">
                    <nord-stack gap="s" align="center">
                      <nord-badge
                        variant={
                          alert.severity === "critical"
                            ? "danger"
                            : alert.severity === "warning"
                            ? "warning"
                            : "neutral"
                        }
                      >
                        {alert.severity}
                      </nord-badge>
                      <span
                        className="font-medium"
                        style={{ color: "var(--n-color-text)" }}
                      >
                        {alert.alertType.replace(/_/g, " ").toUpperCase()}
                      </span>
                    </nord-stack>
                    <p style={{ color: "var(--n-color-text-weak)" }}>
                      {alert.message}
                    </p>
                    <p
                      className="text-xs"
                      style={{ color: "var(--n-color-text-weak)" }}
                    >
                      {new Date(alert.createdAt).toLocaleString()}
                    </p>
                  </nord-stack>
                  {!alert.isAcknowledged && (
                    <nord-badge variant="warning" size="s">
                      Unacknowledged
                    </nord-badge>
                  )}
                </nord-stack>
              </nord-card>
            ))}
          </nord-stack>
        ) : (
          <nord-empty-state size="s">
            <nord-icon
              slot="icon"
              name="interface-checked"
              size="l"
            ></nord-icon>
            <h4 slot="header">No Recent Alerts</h4>
            <p slot="description">This device has no recent alerts.</p>
          </nord-empty-state>
        )}
      </nord-card>

      {/* Historical Data Summary */}
      <nord-card padding="l">
        <h2 slot="header" style={{ color: "var(--n-color-text)" }}>
          Historical Data Summary
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <p
              className="text-2xl font-bold"
              style={{ color: "var(--n-color-text)" }}
            >
              {deviceReadings.length}
            </p>
            <p
              className="text-sm"
              style={{ color: "var(--n-color-text-weak)" }}
            >
              Total Readings
            </p>
          </div>
          <div className="text-center">
            <p
              className="text-2xl font-bold"
              style={{ color: "var(--n-color-text)" }}
            >
              {deviceAlerts.length}
            </p>
            <p
              className="text-sm"
              style={{ color: "var(--n-color-text-weak)" }}
            >
              Total Alerts
            </p>
          </div>
          <div className="text-center">
            <p
              className="text-2xl font-bold"
              style={{ color: "var(--n-color-text)" }}
            >
              {deviceAlerts.filter((a) => !a.isAcknowledged).length}
            </p>
            <p
              className="text-sm"
              style={{ color: "var(--n-color-text-weak)" }}
            >
              Unacknowledged Alerts
            </p>
          </div>
        </div>
      </nord-card>
    </nord-stack>
  );
}
