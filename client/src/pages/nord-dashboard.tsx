import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { NordDeviceCard } from "@/components/nord-device-card";
import { connectWebSocket } from "@/lib/websocket";

interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  mqttTopic: string;
  hospital: string;
  location: string;
  status: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface SensorReading {
  id: number;
  deviceId: number;
  leftBankPressure: string;
  rightBankPressure: string;
  leftBankStatus: string;
  rightBankStatus: string;
  serialNumber: string;
  timestamp: string;
}

export default function NordDashboard() {
  const [latestReadings, setLatestReadings] = useState<SensorReading[]>([]);

  const { data: devices = [], isLoading } = useQuery<Device[]>({
    queryKey: ["/api/devices"],
  });

  const { data: initialReadings = [] } = useQuery<SensorReading[]>({
    queryKey: ["/api/sensor-readings/latest"],
  });

  useEffect(() => {
    if (initialReadings.length > 0) {
      setLatestReadings(initialReadings);
    }
  }, [initialReadings]);

  useEffect(() => {
    const disconnect = connectWebSocket((data) => {
      if (data.type === "sensor-reading") {
        setLatestReadings((prev) => {
          const updated = [...prev];
          const index = updated.findIndex(
            (r) => r.deviceId === data.data.deviceId
          );
          if (index >= 0) {
            updated[index] = data.data;
          } else {
            updated.push(data.data);
          }
          return updated;
        });
      }
    });

    return disconnect;
  }, []);

  const activeDevices = devices.filter((d) => d.isActive);
  const alarmingDevices = devices.filter((d) => d.status === "Alarm");

  if (isLoading) {
    return (
      <nord-stack gap="l" direction="vertical">
        <nord-skeleton
          animate
          lines={1}
          style={{ height: "120px" }}
        ></nord-skeleton>
        <nord-skeleton animate lines={10}></nord-skeleton>
      </nord-stack>
    );
  }

  return (
    <nord-stack gap="l" direction="vertical">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Total Devices
            </p>
            <span
              style={{
                fontSize: "2rem",
                fontWeight: "bold",
                color: "var(--n-color-text)",
              }}
            >
              {devices.length}
            </span>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Active Devices
            </p>
            <span
              style={{
                fontSize: "2rem",
                fontWeight: "bold",
                color: "var(--n-color-status-success)",
              }}
            >
              {activeDevices.length}
            </span>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Alarms
            </p>
            <span
              style={{
                fontSize: "2rem",
                fontWeight: "bold",
                color: "var(--n-color-status-danger)",
              }}
            >
              {alarmingDevices.length}
            </span>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Last Update
            </p>
            <span
              style={{
                fontSize: "1rem",
                fontWeight: "500",
                color: "var(--n-color-text)",
              }}
            >
              {new Date().toLocaleTimeString()}
            </span>
          </nord-stack>
        </nord-card>
      </div>

      {/* Real-time Device Status */}
      <h2
        style={{
          fontSize: "1.5rem",
          fontWeight: "bold",
          color: "var(--n-color-text)",
        }}
      >
        Device Status
      </h2>

      {devices.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {devices.map((device) => {
            const reading = latestReadings.find(
              (r) => r.deviceId === device.id
            );
            return (
              <NordDeviceCard
                key={device.id}
                device={device}
                reading={reading}
              />
            );
          })}
        </div>
      ) : (
        <nord-empty-state>
          <nord-icon
            slot="icon"
            name="interface-content-tree"
            size="xl"
          ></nord-icon>
          <h3 slot="header">No devices configured</h3>
          <p slot="description">
            Add devices from the device management page to start monitoring
          </p>
        </nord-empty-state>
      )}
    </nord-stack>
  );
}
