import { useQuery } from "@tanstack/react-query";
import { useState } from "react";

interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  mqttTopic: string;
  hospital: string;
  location: string;
  status: string;
}

interface SensorReading {
  id: number;
  deviceId: number;
  leftBankPressure: string;
  rightBankPressure: string;
  leftBankStatus: string;
  rightBankStatus: string;
  timestamp: string;
}

export default function NordAnalytics() {
  const [selectedDevice, setSelectedDevice] = useState<string>("all");
  const [dateRange, setDateRange] = useState("7d");

  const { data: devices = [] } = useQuery<Device[]>({
    queryKey: ["/api/devices"],
  });

  const { data: sensorReadings = [] } = useQuery<SensorReading[]>({
    queryKey: ["/api/sensor-readings"],
  });

  // Calculate statistics
  const totalReadings = sensorReadings.length;
  const averagePressure =
    sensorReadings.length > 0
      ? (
          sensorReadings.reduce(
            (sum, r) =>
              sum +
              parseFloat(r.leftBankPressure) +
              parseFloat(r.rightBankPressure),
            0
          ) /
          (sensorReadings.length * 2)
        ).toFixed(1)
      : "0";

  const alertCount = sensorReadings.filter(
    (r) => r.leftBankStatus === "critical" || r.rightBankStatus === "critical"
  ).length;

  const normalCount = sensorReadings.filter(
    (r) => r.leftBankStatus === "normal" && r.rightBankStatus === "normal"
  ).length;

  return (
    <nord-stack gap="l" direction="vertical">
      {/* Filters */}
      <nord-card padding="l">
        <nord-stack gap="m" wrap>
          <nord-select
            label="Device"
            value={selectedDevice}
            onInput={(e: any) => setSelectedDevice(e.target.value)}
            style={{ minWidth: "200px" }}
          >
            <option value="all">All Devices</option>
            {devices.map((device) => (
              <option key={device.id} value={device.id}>
                {device.deviceId}
              </option>
            ))}
          </nord-select>

          <nord-select
            label="Time Range"
            value={dateRange}
            onInput={(e: any) => setDateRange(e.target.value)}
            style={{ minWidth: "200px" }}
          >
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </nord-select>
        </nord-stack>
      </nord-card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Total Readings
            </p>
            <span
              style={{
                fontSize: "2rem",
                fontWeight: "bold",
                color: "var(--n-color-text)",
              }}
            >
              {totalReadings}
            </span>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Average Pressure
            </p>
            <span
              style={{
                fontSize: "2rem",
                fontWeight: "bold",
                color: "var(--n-color-accent)",
              }}
            >
              {averagePressure} PSI
            </span>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Normal Readings
            </p>
            <span
              style={{
                fontSize: "2rem",
                fontWeight: "bold",
                color: "var(--n-color-status-success)",
              }}
            >
              {normalCount}
            </span>
          </nord-stack>
        </nord-card>

        <nord-card padding="l">
          <nord-stack gap="s" direction="vertical">
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Critical Alerts
            </p>
            <span
              style={{
                fontSize: "2rem",
                fontWeight: "bold",
                color: "var(--n-color-status-danger)",
              }}
            >
              {alertCount}
            </span>
          </nord-stack>
        </nord-card>
      </div>

      {/* Charts Section */}
      <nord-card padding="l">
        <h3
          className="text-lg font-semibold mb-4"
          style={{ color: "var(--n-color-text)" }}
        >
          Pressure Trends
        </h3>
        <div
          style={{
            height: "300px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "var(--n-color-surface-raised)",
            borderRadius: "var(--n-border-radius)",
          }}
        >
          <nord-stack gap="s" direction="vertical" align="center">
            <nord-icon
              name="graph-line"
              size="xl"
              color="var(--n-color-text-weak)"
            ></nord-icon>
            <p style={{ color: "var(--n-color-text-weak)" }}>
              Chart visualization would appear here
            </p>
            <p
              style={{
                color: "var(--n-color-text-weak)",
                fontSize: "0.875rem",
              }}
            >
              Showing pressure readings over time
            </p>
          </nord-stack>
        </div>
      </nord-card>

      {/* Device Performance */}
      <nord-card padding="l">
        <h3
          className="text-lg font-semibold mb-4"
          style={{ color: "var(--n-color-text)" }}
        >
          Device Performance Summary
        </h3>
        <nord-table>
          <table>
            <thead>
              <tr>
                <th>Device</th>
                <th>Total Readings</th>
                <th>Normal</th>
                <th>Warnings</th>
                <th>Critical</th>
                <th>Uptime</th>
              </tr>
            </thead>
            <tbody>
              {devices.map((device) => {
                const deviceReadings = sensorReadings.filter(
                  (r) => r.deviceId === device.id
                );
                const normal = deviceReadings.filter(
                  (r) =>
                    r.leftBankStatus === "normal" &&
                    r.rightBankStatus === "normal"
                ).length;
                const warning = deviceReadings.filter(
                  (r) =>
                    r.leftBankStatus === "warning" ||
                    r.rightBankStatus === "warning"
                ).length;
                const critical = deviceReadings.filter(
                  (r) =>
                    r.leftBankStatus === "critical" ||
                    r.rightBankStatus === "critical"
                ).length;

                return (
                  <tr key={device.id}>
                    <td>
                      <nord-stack gap="s" align="center">
                        <nord-icon
                          name="medical-device"
                          color="var(--n-color-accent)"
                        ></nord-icon>
                        <div>
                          <div style={{ fontWeight: "500" }}>
                            {device.deviceId}
                          </div>
                          <div
                            style={{
                              fontSize: "0.875rem",
                              color: "var(--n-color-text-weak)",
                            }}
                          >
                            {device.location}
                          </div>
                        </div>
                      </nord-stack>
                    </td>
                    <td>{deviceReadings.length}</td>
                    <td>
                      <nord-badge variant="success" size="s">
                        {normal}
                      </nord-badge>
                    </td>
                    <td>
                      <nord-badge variant="warning" size="s">
                        {warning}
                      </nord-badge>
                    </td>
                    <td>
                      <nord-badge variant="danger" size="s">
                        {critical}
                      </nord-badge>
                    </td>
                    <td>
                      <nord-progress-bar
                        value={
                          deviceReadings.length > 0
                            ? (normal / deviceReadings.length) * 100
                            : 0
                        }
                        max={100}
                        size="s"
                      ></nord-progress-bar>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </nord-table>
      </nord-card>
    </nord-stack>
  );
}
