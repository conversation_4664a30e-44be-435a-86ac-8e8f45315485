import { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { queryClient } from "@/lib/queryClient";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface AddDeviceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MQTTDeviceData {
  SERNO?: string;
  DEVID?: string;
  "Hospital Name"?: string;
  Location?: string;
}

export default function AddDeviceModal({
  isOpen,
  onClose,
}: AddDeviceModalProps) {
  const [mqttTopic, setMqttTopic] = useState("");
  const [isListening, setIsListening] = useState(false);
  const [deviceData, setDeviceData] = useState<MQTTDeviceData | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const { toast } = useToast();

  // WebSocket connection for listening to MQTT data
  useEffect(() => {
    if (!isListening || !mqttTopic) return;

    const ws = new WebSocket("ws://localhost:3000");

    ws.onopen = () => {
      console.log("WebSocket connected for device setup");
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === "mqtt_message" && data.topic === mqttTopic) {
          const mqttData = data.message;
          setDeviceData({
            SERNO: mqttData.SERNO,
            DEVID: mqttData.DEVID,
            "Hospital Name": mqttData["Hospital Name"],
            Location: mqttData.Location,
          });
          setIsListening(false);
          toast({
            title: "Device Data Received",
            description:
              "Device information has been auto-populated from MQTT data.",
          });
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      toast({
        title: "Connection Error",
        description: "Failed to connect to real-time data stream.",
        variant: "destructive",
      });
      setIsListening(false);
    };

    return () => {
      ws.close();
    };
  }, [isListening, mqttTopic, toast]);

  const handleStartListening = () => {
    if (!mqttTopic.trim()) {
      toast({
        title: "MQTT Topic Required",
        description: "Please enter an MQTT topic first.",
        variant: "destructive",
      });
      return;
    }
    setIsListening(true);
    setDeviceData(null);
  };

  const handleCreateDevice = async () => {
    if (!deviceData || !mqttTopic) {
      toast({
        title: "Missing Data",
        description: "Please listen for device data first.",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const devicePayload = {
        deviceId: deviceData.DEVID || "",
        deviceType: "medical_gas_alarm",
        mqttTopic: mqttTopic,
        hospital: deviceData["Hospital Name"] || "",
        location: deviceData.Location || "",
      };

      await apiRequest("POST", "/api/devices", devicePayload);

      queryClient.invalidateQueries({ queryKey: ["/api/devices"] });
      toast({
        title: "Device Created",
        description:
          "The device has been successfully created and is now monitoring.",
      });

      // Reset form
      setMqttTopic("");
      setDeviceData(null);
      setIsListening(false);
      onClose();
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: "Failed to create device. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Add New Device</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Enter MQTT Topic */}
          <div>
            <Label htmlFor="mqtt-topic">MQTT Topic</Label>
            <div className="flex space-x-2 mt-1">
              <Input
                id="mqtt-topic"
                placeholder="cylinder_manifold/02"
                value={mqttTopic}
                onChange={(e) => setMqttTopic(e.target.value)}
                disabled={isListening}
                className="flex-1"
              />
              <Button
                onClick={handleStartListening}
                disabled={isListening || !mqttTopic.trim()}
                className="bg-medical-blue hover:bg-blue-600"
              >
                {isListening ? "Listening..." : "Listen"}
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Enter the MQTT topic and click "Listen" to auto-populate device
              information.
            </p>
          </div>

          {/* Step 2: Show listening status */}
          {isListening && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-blue-800 font-medium">
                  Listening for device data on topic: {mqttTopic}
                </span>
              </div>
              <p className="text-blue-600 text-sm mt-1">
                Waiting for MQTT message to auto-populate device information...
              </p>
            </div>
          )}

          {/* Step 3: Show received device data */}
          {deviceData && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="font-medium text-green-800 mb-3">
                Device Information Received
              </h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="text-gray-600">Device ID:</span>
                  <div className="font-medium">{deviceData.DEVID || "N/A"}</div>
                </div>
                <div>
                  <span className="text-gray-600">Serial Number:</span>
                  <div className="font-medium">{deviceData.SERNO || "N/A"}</div>
                </div>
                <div>
                  <span className="text-gray-600">Hospital:</span>
                  <div className="font-medium">
                    {deviceData["Hospital Name"] || "N/A"}
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">Location:</span>
                  <div className="font-medium">
                    {deviceData.Location || "N/A"}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Action buttons */}
          <div className="flex items-center space-x-4 pt-4">
            <Button
              onClick={handleCreateDevice}
              disabled={!deviceData || isCreating}
              className="bg-medical-blue hover:bg-blue-600"
            >
              {isCreating ? "Creating Device..." : "Create Device"}
            </Button>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
