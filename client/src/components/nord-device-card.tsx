interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  mqttTopic: string;
  hospital: string;
  location: string;
  status: string;
  updatedAt: string;
}

interface SensorReading {
  leftBankPressure: string;
  rightBankPressure: string;
  leftBankStatus: string;
  rightBankStatus: string;
  serialNumber: string;
  timestamp: string;
}

interface NordDeviceCardProps {
  device: Device;
  reading?: SensorReading;
  onClick?: () => void;
}

export function NordDeviceCard({
  device,
  reading,
  onClick,
}: NordDeviceCardProps) {
  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "normal":
      case "online":
        return "success";
      case "warning":
        return "warning";
      case "critical":
      case "offline":
        return "danger";
      default:
        return "neutral";
    }
  };

  return (
    <nord-card
      padding="l"
      style={{ cursor: onClick ? "pointer" : "default" }}
      onClick={onClick}
    >
      <div slot="header">
        <nord-stack>
          <h3
            className="font-semibold"
            style={{ color: "var(--n-color-text)" }}
          >
            {device.deviceId}
          </h3>
          <nord-badge variant={getStatusVariant(device.status)}>
            {device.status}
          </nord-badge>
        </nord-stack>
      </div>

      <nord-stack gap="s" direction="vertical">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p
              className="text-sm"
              style={{ color: "var(--n-color-text-weak)" }}
            >
              Type
            </p>
            <p style={{ color: "var(--n-color-text)" }}>{device.deviceType}</p>
          </div>
          <div>
            <p
              className="text-sm"
              style={{ color: "var(--n-color-text-weak)" }}
            >
              Location
            </p>
            <p style={{ color: "var(--n-color-text)" }}>{device.location}</p>
          </div>
        </div>

        {reading && (
          <>
            <nord-divider></nord-divider>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p
                  className="text-sm"
                  style={{ color: "var(--n-color-text-weak)" }}
                >
                  Left Bank
                </p>
                <nord-stack gap="xs" align="center">
                  <p style={{ color: "var(--n-color-text)" }}>
                    {reading.leftBankPressure}
                  </p>
                  <nord-badge
                    size="s"
                    variant={getStatusVariant(reading.leftBankStatus)}
                  >
                    {reading.leftBankStatus}
                  </nord-badge>
                </nord-stack>
              </div>
              <div>
                <p
                  className="text-sm"
                  style={{ color: "var(--n-color-text-weak)" }}
                >
                  Right Bank
                </p>
                <nord-stack gap="xs" align="center">
                  <p style={{ color: "var(--n-color-text)" }}>
                    {reading.rightBankPressure}
                  </p>
                  <nord-badge
                    size="s"
                    variant={getStatusVariant(reading.rightBankStatus)}
                  >
                    {reading.rightBankStatus}
                  </nord-badge>
                </nord-stack>
              </div>
            </div>
          </>
        )}

        <div className="mt-2">
          <p className="text-xs" style={{ color: "var(--n-color-text-weak)" }}>
            Last updated: {new Date(device.updatedAt).toLocaleString()}
          </p>
        </div>
      </nord-stack>
    </nord-card>
  );
}
