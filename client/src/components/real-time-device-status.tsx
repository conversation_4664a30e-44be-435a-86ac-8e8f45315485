import { useEffect, useState } from "react";
import { connectWebSocket } from "@/lib/websocket";

interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  mqttTopic: string;
  hospital: string;
  location: string;
  status: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface RealTimeDeviceStatusProps {
  device: Device;
  showDetails?: boolean;
}

export function RealTimeDeviceStatus({
  device: initialDevice,
  showDetails = false,
}: RealTimeDeviceStatusProps) {
  const [device, setDevice] = useState<Device>(initialDevice);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    const cleanup = connectWebSocket((data) => {
      setIsConnected(true);
      if (data.type === "device_update" && data.deviceId === device.id) {
        setDevice(data.device);
        setLastUpdate(new Date());
      }
    });

    return cleanup;
  }, [device.id]);

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "normal":
      case "online":
        return "success";
      case "warning":
        return "warning";
      case "critical":
      case "offline":
        return "danger";
      default:
        return "neutral";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "normal":
      case "online":
        return "interface-checked";
      case "warning":
        return "navigation-warning";
      case "critical":
      case "offline":
        return "navigation-close-circle";
      default:
        return "interface-help";
    }
  };

  const getConnectionStatus = () => {
    if (!isConnected) {
      return { variant: "warning", text: "Connecting...", icon: "interface-loading" };
    }
    if (!device.isActive) {
      return { variant: "neutral", text: "Inactive", icon: "interface-pause" };
    }
    if (lastUpdate && Date.now() - lastUpdate.getTime() > 60000) {
      return { variant: "warning", text: "Stale", icon: "navigation-warning" };
    }
    return { variant: "success", text: "Live", icon: "interface-checked" };
  };

  const connectionStatus = getConnectionStatus();

  return (
    <nord-card padding={showDetails ? "l" : "m"}>
      <div slot="header">
        <nord-stack gap="m" align="center" justify="space-between">
          <nord-stack gap="s" align="center">
            <nord-icon
              name={getStatusIcon(device.status)}
              size="s"
              color={`var(--n-color-status-${getStatusVariant(device.status) === 'danger' ? 'danger' : getStatusVariant(device.status) === 'warning' ? 'warning' : 'success'})`}
            ></nord-icon>
            <h4 style={{ color: "var(--n-color-text)", margin: 0 }}>
              {device.deviceId}
            </h4>
          </nord-stack>
          <nord-stack gap="s" align="center">
            <nord-badge variant={getStatusVariant(device.status)} size="s">
              {device.status}
            </nord-badge>
            <nord-badge variant={connectionStatus.variant} size="s">
              <nord-icon
                name={connectionStatus.icon}
                size="xs"
                slot="start"
              ></nord-icon>
              {connectionStatus.text}
            </nord-badge>
          </nord-stack>
        </nord-stack>
      </div>

      {showDetails && (
        <nord-stack gap="m" direction="vertical">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p
                className="text-sm font-medium"
                style={{ color: "var(--n-color-text-weak)" }}
              >
                Device Type
              </p>
              <p style={{ color: "var(--n-color-text)" }}>{device.deviceType}</p>
            </div>
            <div>
              <p
                className="text-sm font-medium"
                style={{ color: "var(--n-color-text-weak)" }}
              >
                MQTT Topic
              </p>
              <p style={{ color: "var(--n-color-text)" }}>{device.mqttTopic}</p>
            </div>
            <div>
              <p
                className="text-sm font-medium"
                style={{ color: "var(--n-color-text-weak)" }}
              >
                Hospital
              </p>
              <p style={{ color: "var(--n-color-text)" }}>{device.hospital}</p>
            </div>
            <div>
              <p
                className="text-sm font-medium"
                style={{ color: "var(--n-color-text-weak)" }}
              >
                Location
              </p>
              <p style={{ color: "var(--n-color-text)" }}>{device.location}</p>
            </div>
          </div>

          <nord-divider></nord-divider>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p
                className="text-sm font-medium"
                style={{ color: "var(--n-color-text-weak)" }}
              >
                Active Status
              </p>
              <nord-stack gap="s" align="center">
                <nord-badge variant={device.isActive ? "success" : "neutral"} size="s">
                  {device.isActive ? "Active" : "Inactive"}
                </nord-badge>
              </nord-stack>
            </div>
            <div>
              <p
                className="text-sm font-medium"
                style={{ color: "var(--n-color-text-weak)" }}
              >
                Last Updated
              </p>
              <p
                className="text-sm"
                style={{ color: "var(--n-color-text)" }}
              >
                {new Date(device.updatedAt).toLocaleString()}
              </p>
            </div>
          </div>

          {lastUpdate && (
            <>
              <nord-divider></nord-divider>
              <div>
                <p
                  className="text-xs"
                  style={{ color: "var(--n-color-text-weak)" }}
                >
                  Real-time update: {lastUpdate.toLocaleString()}
                </p>
              </div>
            </>
          )}
        </nord-stack>
      )}
    </nord-card>
  );
}
