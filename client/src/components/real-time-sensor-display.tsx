import { useEffect, useState } from "react";
import { connectWebSocket } from "@/lib/websocket";

interface SensorReading {
  id: number;
  deviceId: number;
  leftBankPressure: string;
  rightBankPressure: string;
  leftBankStatus: string;
  rightBankStatus: string;
  leftBankCapacity?: string;
  rightBankCapacity?: string;
  leftBankPercentage?: string;
  rightBankPercentage?: string;
  leftBankSourceType?: string;
  rightBankSourceType?: string;
  hospitalName?: string;
  location?: string;
  serialNumber: string;
  timestamp: string;
}

interface RealTimeSensorDisplayProps {
  deviceId: number;
  initialReading?: SensorReading;
  showExtendedData?: boolean;
}

export function RealTimeSensorDisplay({
  deviceId,
  initialReading,
  showExtendedData = false,
}: RealTimeSensorDisplayProps) {
  const [currentReading, setCurrentReading] = useState<SensorReading | undefined>(
    initialReading
  );
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    const cleanup = connectWebSocket((data) => {
      setIsConnected(true);
      if (data.type === "sensor_update" && data.deviceId === deviceId) {
        setCurrentReading(data.reading);
        setLastUpdate(new Date());
      }
    });

    return cleanup;
  }, [deviceId]);

  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "normal":
      case "online":
        return "success";
      case "warning":
        return "warning";
      case "critical":
      case "offline":
        return "danger";
      default:
        return "neutral";
    }
  };

  const getConnectionStatus = () => {
    if (!isConnected) {
      return { variant: "warning", text: "Connecting..." };
    }
    if (lastUpdate && Date.now() - lastUpdate.getTime() > 30000) {
      return { variant: "warning", text: "Stale Data" };
    }
    return { variant: "success", text: "Live" };
  };

  const connectionStatus = getConnectionStatus();

  if (!currentReading) {
    return (
      <nord-card padding="l">
        <nord-empty-state size="s">
          <nord-icon slot="icon" name="interface-loading" size="l"></nord-icon>
          <h4 slot="header">Loading Sensor Data</h4>
          <p slot="description">Waiting for real-time sensor readings...</p>
        </nord-empty-state>
      </nord-card>
    );
  }

  return (
    <nord-card padding="l">
      <div slot="header">
        <nord-stack gap="m" align="center" justify="space-between">
          <h3 style={{ color: "var(--n-color-text)", margin: 0 }}>
            Real-time Sensor Data
          </h3>
          <nord-stack gap="s" align="center">
            <nord-badge variant={connectionStatus.variant} size="s">
              {connectionStatus.text}
            </nord-badge>
            {lastUpdate && (
              <span
                className="text-xs"
                style={{ color: "var(--n-color-text-weak)" }}
              >
                Updated: {lastUpdate.toLocaleTimeString()}
              </span>
            )}
          </nord-stack>
        </nord-stack>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Bank */}
        <div>
          <h4
            className="text-lg font-medium mb-4"
            style={{ color: "var(--n-color-text)" }}
          >
            Left Bank
          </h4>
          <nord-stack gap="m" direction="vertical">
            <nord-stack gap="m" align="center" justify="space-between">
              <span style={{ color: "var(--n-color-text-weak)" }}>
                Pressure
              </span>
              <span
                className="font-medium text-lg"
                style={{ color: "var(--n-color-text)" }}
              >
                {currentReading.leftBankPressure} Kgcm²
              </span>
            </nord-stack>
            <nord-stack gap="m" align="center" justify="space-between">
              <span style={{ color: "var(--n-color-text-weak)" }}>Status</span>
              <nord-badge
                variant={getStatusVariant(currentReading.leftBankStatus)}
              >
                {currentReading.leftBankStatus}
              </nord-badge>
            </nord-stack>
            {showExtendedData && currentReading.leftBankCapacity && (
              <nord-stack gap="m" align="center" justify="space-between">
                <span style={{ color: "var(--n-color-text-weak)" }}>
                  Capacity
                </span>
                <span style={{ color: "var(--n-color-text)" }}>
                  {currentReading.leftBankCapacity} Kgcm²
                </span>
              </nord-stack>
            )}
            {showExtendedData && currentReading.leftBankPercentage && (
              <nord-stack gap="m" align="center" justify="space-between">
                <span style={{ color: "var(--n-color-text-weak)" }}>
                  Percentage
                </span>
                <span style={{ color: "var(--n-color-text)" }}>
                  {currentReading.leftBankPercentage}%
                </span>
              </nord-stack>
            )}
            {showExtendedData && currentReading.leftBankSourceType && (
              <nord-stack gap="m" align="center" justify="space-between">
                <span style={{ color: "var(--n-color-text-weak)" }}>
                  Source Type
                </span>
                <span style={{ color: "var(--n-color-text)" }}>
                  {currentReading.leftBankSourceType}
                </span>
              </nord-stack>
            )}
          </nord-stack>
        </div>

        {/* Right Bank */}
        <div>
          <h4
            className="text-lg font-medium mb-4"
            style={{ color: "var(--n-color-text)" }}
          >
            Right Bank
          </h4>
          <nord-stack gap="m" direction="vertical">
            <nord-stack gap="m" align="center" justify="space-between">
              <span style={{ color: "var(--n-color-text-weak)" }}>
                Pressure
              </span>
              <span
                className="font-medium text-lg"
                style={{ color: "var(--n-color-text)" }}
              >
                {currentReading.rightBankPressure} Kgcm²
              </span>
            </nord-stack>
            <nord-stack gap="m" align="center" justify="space-between">
              <span style={{ color: "var(--n-color-text-weak)" }}>Status</span>
              <nord-badge
                variant={getStatusVariant(currentReading.rightBankStatus)}
              >
                {currentReading.rightBankStatus}
              </nord-badge>
            </nord-stack>
            {showExtendedData && currentReading.rightBankCapacity && (
              <nord-stack gap="m" align="center" justify="space-between">
                <span style={{ color: "var(--n-color-text-weak)" }}>
                  Capacity
                </span>
                <span style={{ color: "var(--n-color-text)" }}>
                  {currentReading.rightBankCapacity} Kgcm²
                </span>
              </nord-stack>
            )}
            {showExtendedData && currentReading.rightBankPercentage && (
              <nord-stack gap="m" align="center" justify="space-between">
                <span style={{ color: "var(--n-color-text-weak)" }}>
                  Percentage
                </span>
                <span style={{ color: "var(--n-color-text)" }}>
                  {currentReading.rightBankPercentage}%
                </span>
              </nord-stack>
            )}
            {showExtendedData && currentReading.rightBankSourceType && (
              <nord-stack gap="m" align="center" justify="space-between">
                <span style={{ color: "var(--n-color-text-weak)" }}>
                  Source Type
                </span>
                <span style={{ color: "var(--n-color-text)" }}>
                  {currentReading.rightBankSourceType}
                </span>
              </nord-stack>
            )}
          </nord-stack>
        </div>
      </div>

      {showExtendedData && (
        <>
          <nord-divider></nord-divider>
          <div className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {currentReading.hospitalName && (
                <div>
                  <p
                    className="text-sm font-medium"
                    style={{ color: "var(--n-color-text-weak)" }}
                  >
                    Hospital
                  </p>
                  <p style={{ color: "var(--n-color-text)" }}>
                    {currentReading.hospitalName}
                  </p>
                </div>
              )}
              {currentReading.location && (
                <div>
                  <p
                    className="text-sm font-medium"
                    style={{ color: "var(--n-color-text-weak)" }}
                  >
                    Location
                  </p>
                  <p style={{ color: "var(--n-color-text)" }}>
                    {currentReading.location}
                  </p>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      <nord-divider></nord-divider>
      <div className="mt-4">
        <p className="text-sm" style={{ color: "var(--n-color-text-weak)" }}>
          Serial: {currentReading.serialNumber} | Last Reading:{" "}
          {new Date(currentReading.timestamp).toLocaleString()}
        </p>
      </div>
    </nord-card>
  );
}
