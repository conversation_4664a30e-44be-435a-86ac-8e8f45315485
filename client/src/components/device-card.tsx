import { cn } from "@/lib/utils";

interface Device {
  id: number;
  deviceId: string;
  deviceType: string;
  mqttTopic: string;
  hospital: string;
  location: string;
  status: string;
  updatedAt: string;
}

interface SensorReading {
  leftBankPressure: string;
  rightBankPressure: string;
  leftBankStatus: string;
  rightBankStatus: string;
  serialNumber: string;
  timestamp: string;
}

interface DeviceCardProps {
  device: Device;
  reading?: SensorReading;
}

export default function DeviceCard({ device, reading }: DeviceCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "normal":
        return "bg-status-normal text-white";
      case "warning":
        return "bg-status-warning text-white";
      case "critical":
        return "bg-status-critical text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getStatusIndicator = (status: string) => {
    switch (status) {
      case "normal":
        return "w-3 h-3 bg-status-normal rounded-full";
      case "warning":
        return "w-3 h-3 bg-status-warning rounded-full animate-pulse";
      case "critical":
        return "w-3 h-3 bg-status-critical rounded-full animate-pulse";
      default:
        return "w-3 h-3 bg-gray-500 rounded-full";
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-medical-blue bg-opacity-10 rounded-lg flex items-center justify-center">
              <i className="fas fa-gas-pump text-medical-blue"></i>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary">
                {device.deviceId}
              </h3>
              <p className="text-sm text-text-secondary">{device.deviceType}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className={getStatusIndicator(device.status)}></div>
            <span
              className={cn(
                "text-sm font-medium px-2 py-1 rounded-full",
                getStatusColor(device.status)
              )}
            >
              {device.status.charAt(0).toUpperCase() + device.status.slice(1)}
            </span>
          </div>
        </div>

        <div className="space-y-3 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-text-secondary">Location:</span>
            <span className="text-sm font-medium text-text-primary">
              {device.location}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-text-secondary">Hospital:</span>
            <span className="text-sm font-medium text-text-primary">
              {device.hospital}
            </span>
          </div>
        </div>

        {reading && (
          <div className="border-t border-gray-200 pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-sm text-text-secondary">Left Bank</p>
                <p
                  className={cn(
                    "text-xl font-bold",
                    reading.leftBankStatus === "Warning"
                      ? "text-status-warning"
                      : reading.leftBankStatus === "Critical"
                      ? "text-status-critical"
                      : "text-text-primary"
                  )}
                >
                  {reading.leftBankPressure}
                </p>
                <p className="text-xs text-text-secondary">Kgcm²</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-text-secondary">Right Bank</p>
                <p
                  className={cn(
                    "text-xl font-bold",
                    reading.rightBankStatus === "Warning"
                      ? "text-status-warning"
                      : reading.rightBankStatus === "Critical"
                      ? "text-status-critical"
                      : "text-text-primary"
                  )}
                >
                  {reading.rightBankPressure}
                </p>
                <p className="text-xs text-text-secondary">Kgcm²</p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-4 flex items-center justify-between text-xs text-text-secondary">
          <span>Last Updated: {formatTimestamp(device.updatedAt)}</span>
          {reading && <span>Serial: {reading.serialNumber}</span>}
        </div>
      </div>
    </div>
  );
}
